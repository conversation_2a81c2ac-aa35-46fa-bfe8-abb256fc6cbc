# See https://help.github.com/ignore-files/ for more about ignoring files.

# dependencies
/node_modules

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
AMPSMiddleWare.log*
.suo*
/Debug
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/bin/Debug/netcoreapp3.1/AMPSMiddleWare.deps.json
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/bin/Debug/netcoreapp3.1/AMPSMiddleWare.deps.json
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/bin/Debug/netcoreapp3.1/AMPSMiddleWare.dll
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/bin/Debug/netcoreapp3.1/AMPSMiddleWare.dll
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/bin/Debug/netcoreapp3.1/AMPSMiddleWare.exe
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/bin/Debug/netcoreapp3.1/AMPSMiddleWare.pdb
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/bin/
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/obj/
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/Logs/
AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/.vs/
MPT_template.xlsx

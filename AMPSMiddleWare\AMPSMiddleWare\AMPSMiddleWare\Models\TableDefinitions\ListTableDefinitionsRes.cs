﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMPSMiddleWare.Models._BaseModels.BaseResponse;
using Newtonsoft.Json;

namespace AMPSMiddleWare.Models.TableDefinitions
{
    public class ListTableDefinitionsRes
    {
        public ResponseHeader Header { get; set; }

        public StoreHeader Store { get; set; }
   
        public List<ResponseMessage> List { get; set; }
    }
}
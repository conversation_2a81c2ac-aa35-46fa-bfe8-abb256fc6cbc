﻿using AMPSMiddleWare.Enums.BaseEnums;
using AMPSMiddleWare.Models._BaseModels.BaseReference;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models._BaseModels.BaseResponse
{
    public class ResponseMessage
    {
        public string ErrorCode { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string FieldName { get; set; }
        public string Message { get; set; }
        public MessageLevel Level { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<string> UniqueIDs { get; set; }
        public string Backend { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<string> CustomData { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> CustomMap { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string EntityPath { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? BatchPosition { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ReferenceUnion EntityReference { get; set; }
    }
}

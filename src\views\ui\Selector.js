import React, { useState, useEffect } from "react";
import Globals from '../../Globals';
import { Link } from 'react-router-dom'
import jsPDF from "jspdf";
import "jspdf-autotable";
import { format } from "date-fns";
import { Row, Col } from "reactstrap";
import HeaderConfigs from './UiConfig';

import {
  Alert,
  UncontrolledAlert,
  Card,
  CardBody,
  CardTitle, Badge, Button, ButtonGroup
} from "reactstrap";


function truncate(str, n) {
  return (str.length > n) ? str.slice(0, n - 1) + '..' : str;
};

function GetLevelString(level) {
  if (level === Globals.Defs.PriceChangeVersionType.CORPORATE) {
    return "(Corp) " + Globals.corporateName;
  } else if (level === Globals.Defs.PriceChangeVersionType.STOREGROUP) {
    return "(StoreGroup) " + Globals.StoreGroupName;
  } else if (level === Globals.Defs.PriceChangeVersionType.STORE) {
    return "(Store) " + Globals.storeName;
  }

}

function GetActionString(action) {
  if (action === Globals.Defs.ActionType.CREATE) {
    return "Create";
  } else if (action === Globals.Defs.ActionType.UPDATE) {
    return "Update";
  } else if (action === Globals.Defs.ActionType.NOACTION) {
    return "No Action";
  } else {
    return "Invalid action";
  }

}

function getOwnerId(owner) {
  if (owner === Globals.Defs.PriceChangeVersionType.CORPORATE) {
    return Globals.corporateUniqueID
  } else if (owner === Globals.Defs.PriceChangeVersionType.STOREGROUP) {
    return Globals.StoreGroupUniqueID;
  } else if (owner === Globals.Defs.PriceChangeVersionType.STORE) {
    return Globals.storeUniqueID;
  }

}

class Report extends React.Component {
  //const Report = () => {

  constructor(props) {
    super(props);

    this.isTier = (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STOREGROUP) ? true : false;
    this.curLevel = (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.GLOBAL) ? "Global" : (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.CORPORATE) ? "CORPORATE" : (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STOREGROUP) ? "STOREGROUP" : "STORE";

    this.orgText = truncate("(Corp) " + Globals.corporateName, 20);
    this.storeText = truncate("(Store) " + Globals.storeName, 20);
    this.yesText = "Yes";
    this.noText = "No";
    this.displayOrgStore = false;
    this.displayYesNo = false;
    this.actionLevelText = null;

    this.yesNoText = null;
    this.orgStoreText = null;
    Globals.ProposedAction = Globals.Defs.ActionType.INVALID;
    Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.INVALID;

    if (Globals.ValidPriceChangeVersion) { console.log("Globals.ValidPriceChangeVersion :  " + Globals.ValidPriceChangeVersion.toString()); }
    if (Globals.IsActivationDateToday) { console.log("Globals.IsActivationDateToday :  " + Globals.IsActivationDateToday.toString()); }
    if (Globals.IsPriceChangeStartDateIsActivationDate) { console.log("Globals.IsPriceChangeStartDateIsActivationDate :  " + Globals.IsPriceChangeStartDateIsActivationDate.toString()); }

    let tmpisReadyToGenerate = false;

    if (Globals.IsActivationDateToday) {
      if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.GLOBAL) {
        Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
        this.displayYesNo = true;
        this.yesNoText = "Select the Level of Price Change version to be created:";
        this.yesText = truncate("Create(Corp): " + Globals.corporateName, 30);
        this.noText = truncate("Create(Store): " + Globals.storeName, 30);
        //this.displayOrgStore = true;
        //this.orgStoreText = "Select the Level Price Change version to be created:"
      } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.CORPORATE) {
        this.yesNoText = "Select the Price Level Option:";
        this.displayYesNo = true;
        this.yesText = truncate("Update(Corp): " + Globals.corporateName, 30);
        this.noText = truncate("Create(Store): " + Globals.storeName, 30);
      } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STOREGROUP) {
        this.yesNoText = "Select the Price Level Option:";
        this.displayYesNo = true;
        this.yesText = truncate("Update(Store group): " + Globals.StoreGroupName, 30);
        this.noText = truncate("Create(Store): " + Globals.storeName, 30);
      } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STORE) {
        if (Globals.IsPriceChangeStartDateIsActivationDate) {
          Globals.ProposedAction = Globals.Defs.ActionType.UPDATE;
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
          this.actionLevelText = "Action: Update, Level: " + GetLevelString(Globals.ProposedLevel);
          Globals.ProposedLevelString = GetLevelString(Globals.ProposedLevel);

        } else {
          Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
          this.actionLevelText = "Action: Create, Level: " + GetLevelString(Globals.ProposedLevel);
          Globals.ProposedLevelString = GetLevelString(Globals.ProposedLevel);
        }

        tmpisReadyToGenerate = true;
      }
    }
    else {
      if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.GLOBAL) {
        Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
        this.displayYesNo = true;
        this.yesNoText = "Select the Level of Price Change version to be created:";
        this.yesText = truncate("Create(Corp): " + Globals.corporateName, 30);
        this.noText = truncate("Create(Store): " + Globals.storeName, 30);
      }
      else {
        if (Globals.IsPriceChangeStartDateIsActivationDate) {
          // Would you like to update Current price change version?
          this.displayOrgStore = true;
          this.orgStoreText = "Would you like to update existing Price version?";
          this.orgText = truncate("YES(Update): " + GetLevelString(Globals.ValidPriceChangeVersion), 35);
          this.storeText = truncate("NO (Skip) ", 20);

        }
        else {
          Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
          if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.CORPORATE) {
            this.displayYesNo = true;
            this.yesNoText = "Select the Level of Price Change version to be created:";
            this.yesText = truncate("Create(Corp): " + Globals.corporateName, 30);
            this.noText = truncate("Create(Store): " + Globals.storeName, 30);

          } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STOREGROUP) {
            this.yesNoText = "Select the Price Level Option:";
            this.displayYesNo = true;
            this.yesText = truncate("Create(Store group): " + Globals.StoreGroupName, 30);
            this.noText = truncate("Create(Store): " + Globals.storeName, 30);

          } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STORE) {
            this.displayYesNo = false;
            Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
            this.actionLevelText = "Action: Create, Level: " + GetLevelString(Globals.ProposedLevel);
            Globals.ProposedLevelString = GetLevelString(Globals.ProposedLevel);
            //this.toggleReadyToGenerate();
            tmpisReadyToGenerate = true;
          }

        }

      }
    }

    this.state = {
      isReadyToGenerate: tmpisReadyToGenerate,
      refresh: false,
    };

    if (tmpisReadyToGenerate === true) {
      this.createPriceChangeReq();
    }

  }

  toggleReadyToGenerate = () => {
    this.setState((prevState) => ({
      isReadyToGenerate: !prevState.isReadyToGenerate,
    }));
  }

  refresh = () => {
    this.setState((prevState) => ({
      refresh: !prevState.refresh,
    }));
  }

  onOrgStoreClick = (yesNo) => {

    if (yesNo) {
      Globals.ProposedAction = Globals.Defs.ActionType.UPDATE;
      Globals.ProposedLevel = Globals.ValidPriceChangeVersion;
      this.actionLevelText = "Action: Update, Level: " + GetLevelString(Globals.ProposedLevel);
      Globals.ProposedLevelString = GetLevelString(Globals.ProposedLevel);
      this.createPriceChangeReq(); // // It seems request body will be same for both create and update
      this.toggleReadyToGenerate();
    } else {

      Globals.ProposedAction = Globals.Defs.ActionType.NOACTION;
      this.actionLevelText = "Action: None.";
      this.toggleReadyToGenerate();

      //this.yesNoText = "Select the Level of Price Change version to be created:";
      //this.displayYesNo = true;
      //this.yesText = truncate("Create(Corp): " + Globals.corporateName, 30);
      //this.noText = truncate("Create(Store): " + Globals.storeName, 30);
      //this.refresh();
    }
    this.displayOrgStore = false;

  }

  onYesNoClick = (yesNo) => {

    this.displayYesNo = false;

    if (Globals.IsActivationDateToday) {
      if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.GLOBAL) {
        Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
        Globals.ProposedLevel = yesNo ? Globals.Defs.PriceChangeVersionType.CORPORATE : Globals.Defs.PriceChangeVersionType.STORE;
      } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.CORPORATE) {
        if (yesNo) {
          Globals.ProposedAction = Globals.Defs.ActionType.UPDATE;
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.CORPORATE;
        } else {
          Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
        }

      } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STOREGROUP) {
        if (yesNo) {
          Globals.ProposedAction = Globals.Defs.ActionType.UPDATE;
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STOREGROUP;
        } else {
          Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
        }
      }
    } else {
      if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.GLOBAL) {
        Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
        if (yesNo) {
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.CORPORATE;
        } else {
          Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
        }
      }
      else {
        ////////////////////////
        if (Globals.IsPriceChangeStartDateIsActivationDate) {
          // This option will not occur
          // For this option first update option will be given, upon selecting -Ve it comes here.
          Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
          if (yesNo) {
            Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.CORPORATE;
          } else {
            Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
          }

        }
        else {
          Globals.ProposedAction = Globals.Defs.ActionType.CREATE;
          if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.CORPORATE) {
            if (yesNo) {
              Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.CORPORATE;
            } else {
              Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
            }

          } else if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.STOREGROUP) {
            if (yesNo) {
              Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STOREGROUP;
            } else {
              Globals.ProposedLevel = Globals.Defs.PriceChangeVersionType.STORE;
            }

          }

        }
        ////////////////////////

      }
    }

    let action = (Globals.ProposedAction === Globals.Defs.ActionType.UPDATE) ? "Update" : "Create";
    this.actionLevelText = "Action: " + action + ", Level: " + GetLevelString(Globals.ProposedLevel);
    Globals.ProposedLevelString = GetLevelString(Globals.ProposedLevel);

    this.createPriceChangeReq(); // It seems request body will be same for both create and update

    /*
        if (yesNo === true) {
          this.yesNoText = "Yes Selected";
        } else {
          this.yesNoText = "No Selected";
        }*/

    this.toggleReadyToGenerate();

  }


  createPriceChangeReq = () => {


    if (Globals.ProposedLevel) { console.log("Globals.ProposedLevel :  " + GetLevelString(Globals.ProposedLevel)); }
    if (Globals.ProposedAction) { console.log("Globals.ProposedAction :  " + GetActionString(Globals.ProposedAction)); }

    console.log("createPriceChangeReq");

    var myObject = JSON.parse(Globals.TemplateData);

    /*
    const currentDate = new Date();

    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');
    const hours = String(currentDate.getHours()).padStart(2, '0');
    const minutes = String(currentDate.getMinutes()).padStart(2, '0');
    const seconds = String(currentDate.getSeconds()).padStart(2, '0');

    const formattedDateTime = month.toString() + day.toString() + year.toString() + hours.toString() + minutes.toString() + seconds.toString();
    const formattedDate = month.toString() + day.toString() + year.toString().substring(2, 4) + hours.toString() + minutes.toString(); */


    let startDate = new Date(Globals.ActivationDate);

    if ((Globals.GlobalPriceChangeStartDate) && (Globals.ProposedAction === Globals.Defs.ActionType.UPDATE)) {
      startDate = new Date(Globals.GlobalPriceChangeStartDate);
    }


    const endDate = null; //startDate.setFullYear(startDate.getFullYear() + 30); 

    const OwnerUniqueID = getOwnerId(Globals.ProposedLevel);

    //console.log(formattedDateTime);

    var jsonTxt = '{ "Header" ';
    jsonTxt += ":";
    jsonTxt += "{";
    jsonTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
    jsonTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
    jsonTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
    jsonTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID + " , ";
    jsonTxt += '"CorrelationID"' + " : " + '"PriceChange"' + " , ";
    jsonTxt += '"Accept"' + " : " + '"application/json"' + " , ";
    jsonTxt += '"Connection"' + " : " + '"keep-alive"';

    jsonTxt += " },";
    jsonTxt += '"Batch"';
    jsonTxt += " : ";
    jsonTxt += " [ ";
    jsonTxt += " { ";
    jsonTxt += '"PriceChangeUniqueID"' + " : " + '"' + Globals.GlobalPricingRecordID + '"' + " , ";
    //jsonTxt += '"PriceChangeUniqueID"' + " : " + '"'  + guid + '"' + " , " ;
    //jsonTxt += '"PriceChangeUniqueID"' + " : " + '"5d1eeb19-ea1a-421f-b022-535ae0e28f9e"' + " , "; 	
    if (Globals.GlobalPriceChangeID) {
      jsonTxt += '"PriceChangeID"' + " : " + Globals.GlobalPriceChangeID + " , ";
    } else {
      jsonTxt += '"PriceChangeID"' + " : null , ";
    }

    jsonTxt += '"Owner"' + " : " + " { ";
    jsonTxt += '"OwnerUniqueID"' + " : " + '"' + OwnerUniqueID + '"';
    jsonTxt += " },";
    jsonTxt += '"StartDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
    //jsonTxt += '"EndDate"' + " : "  +   '"'  +     + '"' +  " , " ;
    jsonTxt += '"EndDate"' + " : " + '"2050-01-01T00:00:00.00"' + " , ";
    jsonTxt += '"POSStartDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
    //jsonTxt += '"POSEndDate"' + " : "  +   '"'  +    + '"' +  " , " ;

    jsonTxt += '"Name"' + " : " + '"' + Globals.GlobalPricingRecordName + '"' + " , ";
    jsonTxt += '"POSNumber"' + " : " + '"' + Globals.GlobalPricingRecordPOSNumber + '"' + " , ";
    //jsonTxt += '"CorporatePOSNumber"' + " : "  +   '"' + Globals.corporatePOSNumber + '"' + " , " ;
    //jsonTxt += '"CorporateName"' + " : "  +   '"' + Globals.corporateName + '"' + " , ";
    jsonTxt += '"MenuItemAssignments"' + " : " + " [ ";

    var CurPCMenuItems = Globals.ValidStorePriceChange.MenuItemAssignments;

    for (let indx = 0; indx < CurPCMenuItems.length; indx++) {

      const item = myObject.find((item) => item.PosNumber.toString() === CurPCMenuItems[indx].MenuItem.MenuItemPOSNumber.toString());
      if (!item) {
        jsonTxt += " { ";
        jsonTxt += '"MenuItem"' + " : " + " { ";
        jsonTxt += '"MenuItemUniqueID"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.MenuItemUniqueID + '"' + " , ";
        if (CurPCMenuItems[indx].MenuItem.MenuItemName.indexOf('"') < 0) {
          jsonTxt += '"MenuItemName"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.MenuItemName + '"' + " , ";
        }

        //jsonTxt += '"MenuItemName"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.MenuItemName + '"' + " , ";
        jsonTxt += '"MenuItemPOSNumber"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.MenuItemPOSNumber + '"' + " , ";
        jsonTxt += '"OwnerUniqueID"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.OwnerUniqueID.toString() + '"' //+ " , ";
        jsonTxt += "},";
        jsonTxt += '"Price"' + " : " + '"' + CurPCMenuItems[indx].Price + '"';
        jsonTxt += "}";

        jsonTxt += ","

      } else {
        console.log("Skipping original Menuitems as it will be updated in Template: " + CurPCMenuItems[indx].MenuItem.MenuItemPOSNumber.MenuItemName);
      }


    }


    for (let i = 0; i < myObject.length; i++) {


      const item = Globals.ItemList.find((item) => item.posNumber.toString() === myObject[i].PosNumber);

      if (item) {

        if (myObject[i].AlaCartePrice.toString().length > 0) {  //Ignore empty price items
          jsonTxt += " { ";
          jsonTxt += '"MenuItem"' + " : " + " { ";
          jsonTxt += '"MenuItemUniqueID"' + " : " + '"' + item.menuItemUniqueID + '"' + " , ";
          if (item.name.indexOf('"') < 0) {
            jsonTxt += '"MenuItemName"' + " : " + '"' + item.name + '"' + " , ";
          }
          //jsonTxt += '"MenuItemName"' + " : " + '"' + item.name.replace(/([^"]*)\"([\w\s]*)\"([^"]*)/, "$1\'$2\'$3") + '"' + " , ";
          jsonTxt += '"MenuItemPOSNumber"' + " : " + '"' + item.posNumber + '"' + " , ";
          jsonTxt += '"OwnerUniqueID"' + " : " + '"' + Globals.storeUniqueID.toString() + '"' //+ " , ";
          jsonTxt += "},";
          jsonTxt += '"Price"' + " : " + '"' + myObject[i].AlaCartePrice + '"';
          jsonTxt += "}";
        } else {
          console.log("Item price is empty " + myObject[i].ItemName);
        }
        if (i < myObject.length - 1) {
          jsonTxt += ","
        }

      } else {
        console.log("Item not found " + myObject[i].ItemName);
        //blnErrFound = true;
        Globals.TemplateErr = Globals.TemplateErr + '\n' + "Item not found " + myObject[i].ItemName;
        Globals.IgnoreRecords = Globals.IgnoreRecords + 1;
      }


    }
    jsonTxt += " ] ";

    if (this.state.isPriceLevel) {
      jsonTxt += "," + Globals.PriceLevelChange;
    }
    jsonTxt += " }]} ";

    Globals.JSONPriceChangeReq = jsonTxt;
    console.log((jsonTxt));

    /* if (blnErrFound) {
       this.toggleErrDiv();
       this.GenerateErrReport();
     } else {
       url = '/update';
     }*/

    // this.toggleDiv();
    // this.toggleValidationComplete();

  }

  updatePriceChangeReq = () => {


    if (Globals.ProposedLevel) { console.log("Globals.ProposedLevel :  " + GetLevelString(Globals.ProposedLevel)); }
    if (Globals.ProposedAction) { console.log("Globals.ProposedAction :  " + GetActionString(Globals.ProposedAction)); }

    console.log("updatePriceChangeReq");

    var myObject = JSON.parse(Globals.TemplateData);

    /*
    const currentDate = new Date();

    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');
    const hours = String(currentDate.getHours()).padStart(2, '0');
    const minutes = String(currentDate.getMinutes()).padStart(2, '0');
    const seconds = String(currentDate.getSeconds()).padStart(2, '0');

    const formattedDateTime = month.toString() + day.toString() + year.toString() + hours.toString() + minutes.toString() + seconds.toString();
    const formattedDate = month.toString() + day.toString() + year.toString().substring(2, 4) + hours.toString() + minutes.toString(); */

    const startDate = new Date(Globals.GlobalPriceChangeStartDate);//new Date(Globals.ActivationDate);

    const endDate = null; //startDate.setFullYear(startDate.getFullYear() + 30); 

    const OwnerUniqueID = getOwnerId(Globals.ProposedLevel);

    //console.log(formattedDateTime);

    var jsonTxt = '{ "Header" ';
    jsonTxt += ":";
    jsonTxt += "{";
    jsonTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
    jsonTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
    jsonTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
    jsonTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID + " , ";
    jsonTxt += '"CorrelationID"' + " : " + '"PriceChange"' + " , ";
    jsonTxt += '"Accept"' + " : " + '"application/json"' + " , ";
    jsonTxt += '"Connection"' + " : " + '"keep-alive"';

    jsonTxt += " },";
    jsonTxt += '"Batch"';
    jsonTxt += " : ";
    jsonTxt += " [ ";
    jsonTxt += " { ";
    jsonTxt += '"PriceChangeUniqueID"' + " : " + '"' + Globals.GlobalPricingRecordID + '"' + " , ";
    //jsonTxt += '"PriceChangeUniqueID"' + " : " + '"'  + guid + '"' + " , " ;
    //jsonTxt += '"PriceChangeUniqueID"' + " : " + '"5d1eeb19-ea1a-421f-b022-535ae0e28f9e"' + " , "; 	
    jsonTxt += '"PriceChangeID"' + " : " + '"MPT_Pricing"' + " , ";
    jsonTxt += '"Owner"' + " : " + " { ";
    jsonTxt += '"OwnerUniqueID"' + " : " + '"' + OwnerUniqueID + '"';
    jsonTxt += " },";
    jsonTxt += '"StartDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
    //jsonTxt += '"EndDate"' + " : "  +   '"'  +     + '"' +  " , " ;
    jsonTxt += '"EndDate"' + " : " + '"2050-01-01T00:00:00.00"' + " , ";
    jsonTxt += '"POSStartDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
    //jsonTxt += '"POSEndDate"' + " : "  +   '"'  +    + '"' +  " , " ;

    jsonTxt += '"Name"' + " : " + '"' + Globals.GlobalPricingRecordName + '"' + " , ";
    jsonTxt += '"POSNumber"' + " : " + '"' + Globals.GlobalPricingRecordPOSNumber + '"' + " , ";
    //jsonTxt += '"CorporatePOSNumber"' + " : "  +   '"' + Globals.corporatePOSNumber + '"' + " , " ;
    //jsonTxt += '"CorporateName"' + " : "  +   '"' + Globals.corporateName + '"' + " , ";
    jsonTxt += '"MenuItemAssignments"' + " : " + " [ ";

    var CurPCMenuItems = Globals.ValidStorePriceChange.MenuItemAssignments;

    for (let indx = 0; indx < CurPCMenuItems.length; indx++) {

      const item = myObject.find((item) => item.PosNumber.toString() === CurPCMenuItems[indx].MenuItem.MenuItemPOSNumber.toString());
      if (!item) {
        jsonTxt += " { ";
        jsonTxt += '"MenuItem"' + " : " + " { ";
        jsonTxt += '"MenuItemUniqueID"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.MenuItemUniqueID + '"' + " , ";
        jsonTxt += '"MenuItemName"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.MenuItemName + '"' + " , ";
        jsonTxt += '"MenuItemPOSNumber"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.MenuItemPOSNumber + '"' + " , ";
        jsonTxt += '"OwnerUniqueID"' + " : " + '"' + CurPCMenuItems[indx].MenuItem.OwnerUniqueID.toString() + '"' //+ " , ";
        jsonTxt += "},";
        jsonTxt += '"Price"' + " : " + '"' + CurPCMenuItems[indx].Price + '"';
        jsonTxt += "}";

      } else {
        console.log("Skipping original Menuitems as it will be updated in Template: " + CurPCMenuItems[indx].MenuItem.MenuItemPOSNumber.MenuItemName);
      }

      jsonTxt += ","


    }


    for (let i = 0; i < myObject.length; i++) {


      const item = Globals.ItemList.find((item) => item.posNumber.toString() === myObject[i].PosNumber);

      if (item) {

        if (myObject[i].AlaCartePrice.toString().length > 0) {  //Ignore empty price items
          jsonTxt += " { ";
          jsonTxt += '"MenuItem"' + " : " + " { ";
          jsonTxt += '"MenuItemUniqueID"' + " : " + '"' + item.menuItemUniqueID + '"' + " , ";
          jsonTxt += '"MenuItemName"' + " : " + '"' + item.name + '"' + " , ";
          jsonTxt += '"MenuItemPOSNumber"' + " : " + '"' + item.posNumber + '"' + " , ";
          jsonTxt += '"OwnerUniqueID"' + " : " + '"' + Globals.storeUniqueID.toString() + '"' //+ " , ";
          jsonTxt += "},";
          jsonTxt += '"Price"' + " : " + '"' + myObject[i].AlaCartePrice + '"';
          jsonTxt += "}";
        } else {
          console.log("Item price is empty " + myObject[i].ItemName);
        }
        if (i < myObject.length - 1) {
          jsonTxt += ","
        }

      } else {
        console.log("Item not found " + myObject[i].ItemName);
        //blnErrFound = true;
        Globals.TemplateErr = Globals.TemplateErr + '\n' + "Item not found " + myObject[i].ItemName;
        Globals.IgnoreRecords = Globals.IgnoreRecords + 1;
      }


    }
    jsonTxt += " ] ";

    if (this.state.isPriceLevel) {
      jsonTxt += "," + Globals.PriceLevelChange;
    }
    jsonTxt += " }]} ";

    Globals.JSONPriceChangeReq = jsonTxt;
    console.log((jsonTxt));

    /* if (blnErrFound) {
       this.toggleErrDiv();
       this.GenerateErrReport();
     } else {
       url = '/update';
     }*/

    // this.toggleDiv();
    // this.toggleValidationComplete();

  }

  render() {

    const { isReadyToGenerate } = this.state;

    return (
      <div >
        <h1>
          <Badge color="secondary">Level Selector</Badge>
        </h1>

        <Card>
          <CardTitle tag="h6" className="border-bottom p-3 mb-0">
            <b>Details</b>
          </CardTitle>
          <CardBody className="">
            <div className="mt-3"  >

              <Alert color="success">
                <p><b>Current Price Change Level :: </b> {this.curLevel}  </p>
                <p><b>Organization Name :: </b> {Globals.corporateName} </p>
                <p style={{ display: (this.isTier) ? 'block' : 'none' }}><b>Store Group Name :: </b> {Globals.StoreGroupName} </p>
                <p><b>Store Name :: </b> {Globals.storeName} </p>
                <p><b>Activation Date :: </b> {Globals.ActivationDate} </p>

              </Alert>

            </div>
          </CardBody>
        </Card>

        <Row style={{ display: (this.displayYesNo) ? 'block' : 'none' }} >
          <Col >
            {this.yesNoText}
            <Card body>
              <div>
                <Button onClick={() => { this.onYesNoClick(true); }} color="secondary" className="ms-3" >
                  {this.yesText}
                </Button>
                <Button onClick={() => { this.onYesNoClick(false); }} color="secondary" className="ms-3" >
                  {this.noText}
                </Button>
              </div>
            </Card>
          </Col>
        </Row>

        <Row style={{ display: (this.displayOrgStore) ? 'block' : 'none' }} >
          <Col md="8" lg="50">
            {this.orgStoreText}
            <Card body>
              <div>
                <Button onClick={() => { this.onOrgStoreClick(true); }} color="secondary" className="ms-3" >
                  {this.orgText}
                </Button>
                <Button onClick={() => { this.onOrgStoreClick(false); }} color="secondary" className="ms-3" >
                  {this.storeText}
                </Button>
              </div>
            </Card>
          </Col>
        </Row>

        <ButtonGroup>


          <Link style={{ pointerEvents: !isReadyToGenerate ? 'none' : '' }} to='/update'>
            <Button disabled={!isReadyToGenerate} color="secondary"><b>Continue</b></Button>
          </Link>

        </ButtonGroup>

        <br />
        <br />
        <br />

        <Alert color="success" style={{ display: (isReadyToGenerate) ? 'block' : 'none' }} >
          <b>{this.actionLevelText}  </b>
        </Alert>

      </div>
    );

  }


};

export default Report;

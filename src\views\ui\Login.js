import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import * as Yup from 'yup';
import { Formik, Form, Field, ErrorMessage, useFormik } from 'formik';
import { Outlet, Link } from "react-router-dom";
import styles from "./LoginLayout.scss";
import Globals from '../../Globals';
import { log } from './Logger';

const burl = process.env.REACT_APP_SERVER_URL
Globals.BaseURL = burl

export default function Login() {

  const nav = useNavigate();

  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");

  const [error, setError] = useState('');


  const refreshPage = () => {
    nav(0);
}
  
  const getSessionID = () => {
	  
    const currentDate = new Date();
	
	const year = currentDate.getFullYear();
	const month = String(currentDate.getMonth() + 1).padStart(2,'0');
	const day = String(currentDate.getDate()).padStart(2,'0');
	const hours = String(currentDate.getHours()).padStart(2,'0');
	const minutes = String(currentDate.getMinutes()).padStart(2,'0');
	const seconds = String(currentDate.getSeconds()).padStart(2,'0');
	
	const formattedDateTime = month.toString() + day.toString() + year.toString() + hours.toString() + minutes.toString() + seconds.toString();
 
	Globals.SessionID = Globals.Username + "_" + formattedDateTime;
	console.log(Globals.SessionID )
  
  }

  const doUserAuth = async (values) => {
    try {

      const { username, password } = values;

      var body = { Username:username, Password:password };

      const requestOptions = {
        method: 'POST',
        headers: {
          'Content-type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          'Accept-Encoding': 'gzip,deflate'
        },
        body: JSON.stringify(body)
      };

      let response = await fetch(Globals.BaseURL + '/api/User/GetUser', requestOptions);
      let data = await response.json();
      console.log(response.status);

      if (response.status >= 400 && response.status < 600) {
        throw new Error("Bad response from server");
      }

      if (response.status === 200) {
        
        if (JSON.parse(data) === "true") {
          Globals.Username = username;
         // Globals.LogEntry = '{  "logLevel": "info",  "message": "User: '+Globals.Username+' has logged in."   }'; 	
		  


		 getSessionID();	
        const result1 = log("Client Version: "+Globals.ApplicationVersion)
         const result = log("User: "+Globals.Username+" has logged in."); 
		 
          Globals.isUserLoggedIn = true; 
		  
          nav("/environment");
        } else {        
          refreshPage();
        }
      }

    } catch (error) {
      // Handle validation error
      console.log(error);
    }
  };

  const validationSchema = Yup.object().shape({
    username: Yup.string().required('Username is required'),
    password: Yup.string().required('Password is required'),
  });

  return (
    <>
      {/* Wrapping form inside formik tag and passing our schema to validationSchema prop */}
      <Formik
        validationSchema={validationSchema}
        initialValues={{ username: "", password: "" }}

        onSubmit={(values) => {
          // Alert the input values of the form that we filled
          //alert(JSON.stringify(values));
          doUserAuth(values);
        }}

      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
        }) => (
          <div className="login">
            <div className="form">
              {/* Passing handleSubmit parameter tohtml form onSubmit property */}
              <form noValidate onSubmit={handleSubmit}>
                <span>Login</span>
                {/* Our input html with passing formik parameters like handleChange, values, handleBlur to input properties */}
                <Field
                  type="username"
                  name="username"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.username}
                  placeholder="Enter username"
                  className="form-control inp_text"
                  id="username"
                />
                {/* If validation is not passed show errors */}
                <p className="error">
                  {errors.username && touched.username && errors.username}
                </p>
                {/* Our input html with passing formik parameters like handleChange, values, handleBlur to input properties */}
                <Field
                  type="password"
                  name="password"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.password}
                  placeholder="Enter password"
                  className="form-control"
                />
                {/* If validation is not passed show errors */}
                <p className="error">
                  {errors.password && touched.password && errors.password}
                </p>
                {/* Click on submit button to submit the form */}
                <button type="submit">Login</button>
              </form>
            </div>
          </div>
        )}
      </Formik>
      <Outlet></Outlet>
    </>
  );
};
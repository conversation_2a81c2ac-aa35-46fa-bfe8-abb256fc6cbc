﻿using AMPSMiddleWare.Models._BaseModels.BaseHeader;
using System;
using System.Collections.Generic;
using System.Text;
using AMPSMiddleWare.Models.Owner;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChangeHeader : HeaderBase
    {
        public Guid PriceChangeUniqueID { get; set; }
        public string PriceChangeID { get; set; }
        public OwnerReference Owner { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? POSStartDate { get; set; }

    }
}

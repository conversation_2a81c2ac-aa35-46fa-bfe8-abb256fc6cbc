{"name": "starter-bt5", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^2.35.0", "@azure/msal-react": "^1.5.5", "@testing-library/jest-dom": "^5.15.0", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "apexcharts": "^3.29.0", "bootstrap": "^5.1.3", "bootstrap-icons": "^1.7.0", "cors": "^2.8.5", "cors-anywhere": "^0.4.4", "date-fns": "^2.29.3", "env-cmd": "^10.1.0", "express": "^4.18.2", "formik": "^2.4.2", "history": "^5.0.0", "http-proxy-middleware": "^2.0.6", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.28", "node-sass": "^8.0.0", "prop-types": "^15.7.2", "react": "^17.0.2", "react-apexcharts": "^1.3.9", "react-dom": "^17.0.2", "react-dropdown": "^1.11.0", "react-faq-component": "^1.3.4", "react-idle-timer": "^5.7.2", "react-microsoft-login": "^2.0.1", "react-router-dom": "^6.0.0-beta.8", "react-scripts": "4.0.3", "reactstrap": "^9.0.0", "styled-components": "^5.3.9", "web-vitals": "^1.1.2", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "scripts": {"start": "env-cmd -e development react-scripts --openssl-legacy-provider start", "start-prod": "env-cmd -e production react-scripts --openssl-legacy-provider start", "start-qa": "env-cmd -e qa react-scripts --openssl-legacy-provider start", "build": "env-cmd -e development react-scripts --openssl-legacy-provider build", "build-prod": "env-cmd -e production react-scripts --openssl-legacy-provider build", "build-qa": "env-cmd -e qa react-scripts --openssl-legacy-provider build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
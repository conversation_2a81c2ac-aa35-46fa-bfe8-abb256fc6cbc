﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.Store
{
    public class ParentOwner
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string OwnerUniqueID { get; set; }
        public string OwnerID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int CorporatePOSNumber { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CorporateName { get; set; }
    }
}

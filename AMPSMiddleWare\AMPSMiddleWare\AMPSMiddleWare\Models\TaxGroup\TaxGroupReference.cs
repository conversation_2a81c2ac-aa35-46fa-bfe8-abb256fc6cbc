﻿using AMPSMiddleWare.Models.Owner;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.TaxGroup
{
    public class TaxGroupReference : OwnerReference
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public Guid TaxGroupUniqueID { get; set; }
        public string TaxGroupID { get; set; }
        public string TaxGroupName { get; set; }
        public int TaxGroupPOSNumber { get; set; }
    }
}

import React, { useState, useEffect } from "react";
import Globals from '../../Globals';
import { useNavigate } from "react-router-dom";
import jsPDF from "jspdf";
import "jspdf-autotable";
import LoadingAnim from "../../assets/images/logos/loading2.gif";
import { loghistory } from './HistoryLogger';
import { IdleTimerProvider, useIdleTimerContext } from 'react-idle-timer/dist/index.legacy.esm.js'
import { log } from './Logger';
import {
	Alert,
	UncontrolledAlert,
	Card,
	CardBody,
	CardTitle, Badge, Button, ButtonGroup
} from "reactstrap";
import HeaderConfigs from './UiConfig';


var response;

const Alerts = () => {
	// For Dismiss Button with Alert
	const [visible, setVisible] = useState(true);
	const [applied, setApplied] = useState(false);
	const [isError, setIsError] = useState(false);

	const onDismiss = () => {
		setVisible(false);
	};
	const navigate = useNavigate();
	const [isloadingDivVisible, setloadingDivVisibility] = useState(false)
	const [isfinalDivVisible, setfinalDivVisibility] = useState(false)

	const idleTimer = useIdleTimerContext()

	useEffect(() => {
		if (!Globals.isUserLoggedIn) {
			navigate('/login');
		}
	}, []);


	const onIdle = () => {
		console.log("user is idle");
		navigate('/login');
	}

	const getSessionID = () => {

		const currentDate = new Date();

		const year = currentDate.getFullYear();
		const month = String(currentDate.getMonth() + 1).padStart(2, '0');
		const day = String(currentDate.getDate()).padStart(2, '0');
		const hours = String(currentDate.getHours()).padStart(2, '0');
		const minutes = String(currentDate.getMinutes()).padStart(2, '0');
		const seconds = String(currentDate.getSeconds()).padStart(2, '0');

		const formattedDateTime = month.toString() + day.toString() + year.toString() + hours.toString() + minutes.toString() + seconds.toString();
		const result = log("End Session");
		Globals.SessionID = Globals.Username + "_" + formattedDateTime;
		console.log(Globals.SessionID)

	}

	const GenerateReport = () => {


		const doc = new jsPDF();
		// define the columns we want and their titles
		const tableColumn = ["Id", "Title", "Issue", "Status", "Closed on"];
		// define an empty array of rows
		const tableRows = [];


		var image = "data:image/png;base64,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";


		doc.addImage(image, 'PNG', 3, -10, 50, 50);





		// startY is basically margin-top
		//doc.autoTable(tableColumn, tableRows, { startY: 20 });
		const date = Date().split(" ");
		// we use a date string to generate our filename.
		const dateStr = date[0] + date[1] + date[2] + date[3] + date[4];
		// ticket title. and margin-top + margin-left
		doc.setFontSize(23)
		doc.text("CFC Mass Price Update Report", 55, 18);
		// we define the name of our PDF file.

		doc.setFontSize(18)
		doc.text("Environment:", 14, 45);

		//doc.text("Run Date/Time :: ", 14, 65);	
		//doc.text(Date().toLocaleString() , 44, 65);	

		doc.setFontSize(12)
		doc.text("Selected Customer :: ", 24, 55);
		doc.text(Globals.SelectedCustomer + "", 65, 55);


		doc.text("Selected Environment :: ", 24, 65);
		doc.text(Globals.SelectedEnvironment + "", 70, 65);

		doc.setFontSize(18)
		doc.text("Import:", 14, 85);

		doc.setFontSize(12)
		doc.text("Template :: ", 24, 95);
		doc.text(Globals.TemplateFileName + "", 64, 95);

		doc.text("Total Records :: ", 24, 105);
		doc.text(Globals.TotalRecords + "", 64, 105);

		doc.text("Successfull :: ", 24, 115);
		doc.text(Globals.TotalRecords + "", 64, 115);

		doc.text("Ignored :: ", 24, 125);
		doc.text(Globals.IgnoreRecords + "", 64, 125);

		doc.setFontSize(15)
		doc.text(Date().toLocaleString(), 14, 145);

		doc.setFontSize(12)
		var successLog = Globals.TypeOfChangeLog + "\n" + Globals.GlobalPricingLog + "\n" + Globals.UpdatedPriceChangeLog + "\n";
		doc.text(successLog, 14, 160);


		doc.save(`CFC_PriceUpdate_report_${dateStr}.pdf`);


	}


	const PriceChanges1 = async () => {



		let response = await fetch(Globals.BaseURL + '/PriceChanges1', {
			method: 'POST',
			body: Globals.TemplateData,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate'
			},
		});
		let data = await response.json();
		console.log(data);

	};


	const resetGlobalLogs = () => {
		Globals.GlobalPricingLog = "";
		Globals.UpdatedPriceChangeLog = "Updated Store Price: \n";
	}

	const priceChange = async () => {

		resetGlobalLogs();

		setloadingDivVisibility(!isloadingDivVisible);

		if (Globals.ProposedAction === Globals.Defs.ActionType.CREATE) {
			Globals.GlobalPricingLog = "Creating the Price change version for " + Globals.GlobalPricingRecordName + "At Level " + Globals.ProposedLevelString;
			createPriceChange();
		} else {
			Globals.GlobalPricingLog = "Updating the Price change version for " + Globals.GlobalPricingRecordName + "At Level " + Globals.ProposedLevelString;
			updatePriceChange();
		}


		/*	else {
	
	
				let response = await fetch(Globals.BaseURL + '/UpdatePriceChanges', {
					method: 'POST',
					body:
	
						Globals.JSONGlobalPriceChangeReq
	
					,
					headers: {
						'Content-type': 'application/json; charset=UTF-8',
						'Accept': 'application/json',
						'Accept-Encoding': 'gzip,deflate',
					},
				});
	
				let data = await response.json();
				console.log(response.status);
				if (response.status === 200) {
					Globals.GlobalPricingLog = 'Created the new global price record.';
					createPriceChange();
				}
	
			} */


	};


	const validatePriceChangeSchedule = async () => {


		setloadingDivVisibility(!isloadingDivVisible);

		//Globals.storeUniqueID = "3c43cd17-49cc-43ca-8f71-a15c7886cd25";

		const startDate = new Date(Globals.ActivationDate);
		var jsonPriceChangeScheduleTxt = '{ "Header" ';
		jsonPriceChangeScheduleTxt += ":";
		jsonPriceChangeScheduleTxt += "{";
		jsonPriceChangeScheduleTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
		jsonPriceChangeScheduleTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
		jsonPriceChangeScheduleTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
		jsonPriceChangeScheduleTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID;
		jsonPriceChangeScheduleTxt += " },";
		jsonPriceChangeScheduleTxt += '"EffectiveDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
		jsonPriceChangeScheduleTxt += '"StoreUniqueID"' + " : " + '"' + Globals.storeUniqueID + '"'
		jsonPriceChangeScheduleTxt += " }";

		let response = await fetch(Globals.BaseURL + '/CollectPriceChangesForStore', {
			method: 'POST',
			body:

				jsonPriceChangeScheduleTxt

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',

			},
		});

		let data = await response.json();

		//console.log(data.List);


		Globals.StorePriceChanges = JSON.stringify(data.List);
		//console.log("StorePriceChanges");
		//console.log(Globals.StorePriceChanges);

		var myObject = JSON.parse(Globals.StorePriceChanges);
		const arrMenuItems = [];
		const arrTemplateItems = [];
		var blnMatchFound = false;
		console.log(myObject.length);

		var myTemplateObject = JSON.parse(Globals.TemplateData);
		for (let i = 0; i < myTemplateObject.length; i++) {

			arrTemplateItems.push({ name: myTemplateObject[i].ItemName, value: parseFloat(myTemplateObject[i].AlaCartePrice) });
		}


		for (let i = 0; i < myObject.length; i++) {


			if (new Date(Globals.ActivationDate).toLocaleString() == new Date(myObject[i].StartDate).toLocaleString()) {

				blnMatchFound = true;

				var menuItems = myObject[i].MenuItemAssignments;

				//console.log(menuItems.length);

				for (let j = 0; j < menuItems.length; j++) {

					//console.log(menuItems[j].Price);
					//console.log(menuItems[j].MenuItem.MenuItemName);
					arrMenuItems.push({ name: menuItems[j].MenuItem.MenuItemName, value: menuItems[j].Price });

					Globals.UpdatedPriceChangeLog = Globals.UpdatedPriceChangeLog + "\t ItemName: " + menuItems[j].MenuItem.MenuItemName + ",  Price: " + menuItems[j].Price + "\n";
				}





			}

		}

		//console.log(arrMenuItems);
		//console.log(arrTemplateItems);

		if (blnMatchFound) {
			const areEqual = arrTemplateItems.every(pair1 => arrMenuItems.find(pair2 => pair2.name === pair1.name && pair2.value === pair1.value));

			if (!areEqual) {
				//Validation Failed	
			}
		} else {

			//Validation Failed
		}
		//console.log(areEqual);


		setloadingDivVisibility(false);
		setfinalDivVisibility(true);
		setApplied(true);


		//setloadingDivVisibility(false);

	};


	const createPriceChange = async () => {

		setloadingDivVisibility(!isloadingDivVisible);

		let response = await fetch(Globals.BaseURL + '/UpdatePriceChanges', {
			method: 'POST',
			body:

				Globals.JSONPriceChangeReq

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',
			},
		});

		let data = await response.json();
		console.log(response.status);
		console.log(response);



		if (response.status === 200) {
			//setloadingDivVisibility(false);		
			//setfinalDivVisibility(true);

			const currentDate = new Date();

			const year = currentDate.getFullYear();
			const month = String(currentDate.getMonth() + 1).padStart(2, '0');
			const day = String(currentDate.getDate()).padStart(2, '0');
			const hours = String(currentDate.getHours()).padStart(2, '0');
			const minutes = String(currentDate.getMinutes()).padStart(2, '0');
			const seconds = String(currentDate.getSeconds()).padStart(2, '0');

			const formattedDateTime = month.toString() + "/" + day.toString() + "/" + year.toString() + " " + hours.toString() + ":" + minutes.toString() + ":" + seconds.toString();

			Globals.LogHistory = '{ "message": "' + Globals.TemplateFileName + ' was uploaded @  ' + formattedDateTime + ' by user "    }';
			const result = loghistory();


			validatePriceChangeSchedule();

		}

		getSessionID();

	};

	const updatePriceChange = async () => {

		setloadingDivVisibility(!isloadingDivVisible);

		let response = await fetch(Globals.BaseURL + '/UpdateCurrentPriceChanges', {
			method: 'POST',
			body:

				//Globals.JSONUpdatePriceChangeReq
				Globals.JSONPriceChangeReq  // same request body for both crete and update.

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',
			},
		});

		let data = await response.json();
		console.log(response.status);



		if (response.status === 200) {
			//setloadingDivVisibility(false);		
			//setfinalDivVisibility(true);

			const currentDate = new Date();

			const year = currentDate.getFullYear();
			const month = String(currentDate.getMonth() + 1).padStart(2, '0');
			const day = String(currentDate.getDate()).padStart(2, '0');
			const hours = String(currentDate.getHours()).padStart(2, '0');
			const minutes = String(currentDate.getMinutes()).padStart(2, '0');
			const seconds = String(currentDate.getSeconds()).padStart(2, '0');

			const formattedDateTime = month.toString() + "/" + day.toString() + "/" + year.toString() + " " + hours.toString() + ":" + minutes.toString() + ":" + seconds.toString();

			Globals.LogHistory = '{ "message": "' + Globals.TemplateFileName + ' was uploaded @  ' + formattedDateTime + ' by user "    }';
			const result = loghistory();


			validatePriceChangeSchedule();

		}

		getSessionID();

	};


	return (





		<div>

			<h1>
				<Badge color="secondary">Apply your changes to CFC</Badge>
			</h1>

			<IdleTimerProvider
				timeout={10000 * 60}
				onIdle={onIdle}

			/>


			<Card>
				<CardTitle tag="h6" className="border-bottom p-3 mb-0">
					<b>Please review and click Apply</b>
				</CardTitle>
				<CardBody className="">
					<div className="mt-3">

						<Alert color="success">
							<b>Selected Customer  :: </b>   {Globals.SelectedCustomer}

						</Alert>
						<Alert color="success">
							<b>Selected Environment :: </b> {Globals.SelectedEnvironment}

						</Alert>
						<Alert color="success">
							<b>Template File Name  :: </b> {Globals.TemplateFileName}

						</Alert>

						<Alert color="success">
							<b>Activation Schedule  :: </b> {Globals.ActivationDate}

						</Alert>

					</div>
				</CardBody>
			</Card>

			{!applied ? <ButtonGroup><Button onClick={() => { priceChange(); }} color="secondary"><b>Apply</b></Button></ButtonGroup> : null}

			{applied ? <ButtonGroup> {isError ? <Button color="secondary"><b>View Error Report</b></Button> : <Button onClick={() => { GenerateReport(); }} color="secondary"><b>View Report</b></Button>}</ButtonGroup> : null}

			<div id="loadingDiv" style={{ display: (isloadingDivVisible) ? 'block' : 'none' }}>
				<br />
				<br />
				<Alert color="danger"> Please wait while we process the file</Alert>
				<br />
				<img src={LoadingAnim} style={{ "position": "relative", top: -220 }} />
			</div>

			<div id="finalDiv" style={{ display: (isfinalDivVisible) ? 'block' : 'none' }}>  <br />
				<br /> <b><Alert color="success">The import and post validation is now complete. <br />
					Click on Reports tab to generate a report copy.
				</Alert> </b></div>

		</div>
	);
};

export default Alerts;

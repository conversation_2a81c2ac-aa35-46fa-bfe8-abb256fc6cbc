

import Globals from '../../Globals';


export function log(message, loglevel="info"){

	Globals.LogEntry = '{ "fileName":"' + Globals.SessionID  +  '", "logLevel": "'+loglevel+'",  "message": "'+message+'" ,"version": "'+Globals.ApplicationVersion+'"   }';
     console.log(Globals.LogEntry);
    logtoserver();
}

const logtoserver = async () => {
	   
	   let response = await fetch(Globals.BaseURL+'/api/Log', { 
	    method: 'POST',
		 body: 
			 
		 Globals.LogEntry
		   
          ,
		  headers: {
            'Content-type': 'application/json; charset=UTF-8',
			'Accept' : 'application/json',
			'Accept-Encoding' : 'gzip,deflate',
			 
         },
	   });
	   
	   let data = await response.json();        
};
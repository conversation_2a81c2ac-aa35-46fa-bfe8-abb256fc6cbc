import React, { useState } from "react";
import Globals from '../../Globals';
import * as XLSX from "xlsx";
import LoadingAnim from "../../assets/images/logos/loading2.gif";
import { log } from './Logger';

import {
	<PERSON>ert,
	UncontrolledAlert,
	Card,
	CardBody,
	CardTitle, Badge, Button, ButtonGroup
} from "reactstrap";


var response;

const Export = () => {
	// For Dismiss Button with <PERSON>ert
	const [visible, setVisible] = useState(true);

	const onDismiss = () => {
		setVisible(false);
	};

	const [isloadingDivVisible, setloadingDivVisibility] = useState(false)
	const [isfinalDivVisible, setfinalDivVisibility] = useState(false)

	 

	const getMenuItems = async () => {

		let response = await fetch(Globals.BaseURL + '/ItemList', {
			method: 'POST',
			body:

				""

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',

			},
		});
		 
		 
		let data = await response.json();
		Globals.ItemList = data.list; 
		 
		console.log(Globals.ItemList) 
		 
		setloadingDivVisibility(!isloadingDivVisible);

		
		
		 
		//setloadingDivVisibility(false);
		//setfinalDivVisibility(true); 
		 
		
		getPriceLevels();
		/*
		const blob = XLSX.writeFile(wb,'MPT_TemplateData.xlsx');
		
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = 'MPT_TemplateData.xlsx';
		a.click();
			
		URL.revokeObjectURL(url);
		*/ 
	}; 
	 
	const getPriceLevels = async () => {

		let response = await fetch(Globals.BaseURL + '/GetPriceLevels', {
			method: 'POST',
			body:

				""

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',

			},
		});
		 
		 
		let data = await response.json();
		
		Globals.PriceLevels = data.list; 
		 
		console.log(Globals.ItemList);
		console.log(Globals.PriceLevels) 
		
		var myObject = Globals.ItemList;
		var jsonData = "[";
		for (let i = 0; i < myObject.length; i++) {
			if(myObject[i].name.length > 0){
				jsonData = jsonData + '{' + '"SiteName" :' + '""' + ',' + '"StartDate" : ' + '""' + ',' + '"PosNumber" : '  + myObject[i].posNumber +  "," +   '"ItemName" : "' + myObject[i].name  + '"' + ',' +  '"AlaCartePrice"  : '  + '""' + '}' 
				
				if(i < myObject.length -1){
					jsonData += ","
				}
			} 
		}
		 jsonData += "]" 
		 
		console.log(jsonData); 	
		 
		const data1 = JSON.parse(jsonData);  
		 
		data1.sort((a,b) => { 
		
		const nameA = a.ItemName.toUpperCase();
		const nameB = b.ItemName.toUpperCase();
		if(nameA < nameB) return -1;
		if(nameA > nameB) return 1;
		return 0;
		
		});
		
		 
		var myPriceLevelObject = Globals.PriceLevels;
		var jsonPriceLevelData = "[";
		for (let i = 0; i < myPriceLevelObject.length; i++) {
			if(myPriceLevelObject[i].name.length > 0){
				jsonPriceLevelData = jsonPriceLevelData + '{' + '"SiteName" :' + '""' + ',' + '"StartDate" : ' + '""' + ',' + '"PosNumber" : '  + myPriceLevelObject[i].posNumber +  "," +   '"ItemName" : "' + myPriceLevelObject[i].name  + '"' + ',' +  '"PriceLevel"  : '  + '""' + '}' 
				
				if(i < myPriceLevelObject.length -1){
					jsonPriceLevelData += ","
				}
			} 
			
		}
		 jsonPriceLevelData += "]"  
		 
		 const data2 = JSON.parse(jsonPriceLevelData);  
		   
		data2.sort((a,b) => { 
		
		const nameA = a.ItemName.toUpperCase();
		const nameB = b.ItemName.toUpperCase();
		if(nameA < nameB) return -1;
		if(nameA > nameB) return 1;
		return 0;
		
		});
		 
		const wb = XLSX.utils.book_new();
		const ws = XLSX.utils.json_to_sheet(data1);
		const ws1 = XLSX.utils.json_to_sheet(data2);
		
		ws['!cols'] = [ {wch: 15} , {wch: 10} , {wch: 20} , {wch: 30} , {wch: 15}];
		ws['C1'].s = { alignment : {horizontal: 'center',vertical: 'center', }, };
		
		ws1['!cols'] = [ {wch: 15} , {wch: 10} , {wch: 20} , {wch: 30} , {wch: 15}];
		ws1['C1'].s = { alignment : {horizontal: 'center',vertical: 'center', }, };
		
		XLSX.utils.book_append_sheet(wb, ws, 'ItemPricing');
		XLSX.utils.book_append_sheet(wb, ws1, 'PriceLevels');
		
		
		
		
		XLSX.writeFile(wb,'MPT_TemplateData.xlsx'); 
		 
		
		 
		setloadingDivVisibility(false);
		setfinalDivVisibility(true); 
		
	};		
 

	 
	return (





		<div>

			<h1>
				<Badge color="secondary">Export Pricing</Badge>
			</h1>

			<Card>
				<CardTitle tag="h6" className="border-bottom p-3 mb-0">
					<b>Please review and click Export</b>
				</CardTitle>
				<CardBody className="">
					<div className="mt-3">

						<Alert color="success">
							<b>Selected Database  :: </b>   {Globals.SelectedCustomer}

						</Alert>
						<Alert color="success">
							<b>Selected Environment :: </b> {Globals.SelectedEnvironment}
						</Alert>


					</div>
				</CardBody>
			</Card>

			<ButtonGroup>

				<Button onClick={() => { getMenuItems(); }} color="secondary"><b>Export</b></Button>

			</ButtonGroup>
			<div id="loadingDiv" style={{ display: (isloadingDivVisible) ? 'block' : 'none' }}>
				<br />
				<br />
				<Alert color="danger"> Please wait while we process your request</Alert>
				<br />
				<img src={LoadingAnim} style={{ "position": "relative", top: -220 }} />
			</div>

			<div id="finalDiv" style={{ display: (isfinalDivVisible) ? 'block' : 'none' }} >  <br />
				<br /> <b><Alert color="success">The export process is now complete.</Alert> </b></div>

		</div>
	);
};

export default Export;

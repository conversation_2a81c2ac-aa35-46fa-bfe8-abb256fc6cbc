﻿using Microsoft.AspNetCore.Mvc;
using System;
using AMPSMiddleWare.Models.Log;
using AMPSMiddleWare.Log;
using log4net;

namespace AMPSMiddleWare.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LogController : ControllerBase
    {

        private readonly Logger _logger = new Logger();

        // Get specific name for client logger in log4net.config
        private static readonly ILog _clientLogger = LogManager.GetLogger("ClientLogger");

        [HttpPost]
        public IActionResult Log(LogEntry logEntry)
        {
            log4net.GlobalContext.Properties["Session"] = logEntry.FileName;
            log4net.GlobalContext.Properties["ClientVersion"] = "Client  "  + logEntry.Version;
          
            UpdateLog4Net(logEntry);


            string logPath = "Logs";
            bool exists = System.IO.Directory.Exists(logPath);

            if (!exists)
            {
                System.IO.Directory.CreateDirectory(logPath);

            }
            string strdate = logEntry.FileName.Split("_")[1].Substring(0, 8);


            if (!System.IO.Directory.Exists(logPath + "\\" + strdate))
            {
                System.IO.Directory.CreateDirectory(logPath + "\\" + strdate);
            }

            try
            {
                string fileName = logPath + "\\" + strdate + "\\" + logEntry.FileName + ".log";
                System.IO.File.AppendAllText(fileName, logEntry.Message + '\n');

               
            }
            catch (Exception e)
            {

            }

            return Ok(new { Message = "Log entry received" });
        }


        private void UpdateLog4Net(LogEntry level)
        {

            try
            {
                switch (level.LogLevel.ToLower())
                {
                    case "info":
                        _clientLogger.Info(level.Message);                       
                        break;

                    case "debug":
                        _clientLogger.Debug(level.Message);
                        break;

                    case "warn":
                        _clientLogger.Warn(level.Message);
                        break;

                    case "error":
                        _clientLogger.Error(level.Message);
                        break;
                    default:
                        break;

                }

            }
            catch (Exception)
            {

            }

        }


    }

}

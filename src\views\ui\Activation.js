import React, { useRef, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import * as XLSX from "xlsx";
import { <PERSON>ert, Badge, Button, Card, CardBody, CardTitle, CardText, Row, Col, UncontrolledDropdown, DropdownToggle, DropdownMenu, DropdownItem, Dropdown, ButtonGroup } from "reactstrap";
import { Link } from 'react-router-dom'
import Globals from '../../Globals';
import { log } from './Logger';
import { IdleTimerProvider, useIdleTimerContext } from 'react-idle-timer/dist/index.legacy.esm.js'


const fileTypes = ["XLSX"];
var url = '/activation';


const DatePicker = () => {
	//const [date, setDate] = useState('');
	const dateInputRef = useRef(null);
	const [date, setDate] = React.useState(null);
	const [isErrDivVisible, setErrDivVisibility] = useState(false)
	const [isWarningDivVisible, setWarnDivVisiblity] = useState(false)
	var jsonTxt = "{}"
	const navigate = useNavigate();

	const idleTimer = useIdleTimerContext()

	useEffect(() => {

		if (!Globals.isUserLoggedIn) {
			navigate('/login');
		}
	}, []);

	const onIdle = () => {
		console.log("user is idle");
		navigate('/login');
	}

	const handleChange = (e) => {

		const currentDate = new Date();
		const formattedCurrentDate = currentDate.toLocaleDateString('en-US', {
			month: '2-digit',
			day: '2-digit',
			year: 'numeric',
		});


		const day = (e.target.value).toString().split("-")[2];
		const month = (e.target.value).toString().split("-")[1].substring(0, 2);
		const year = (e.target.value).toString().split("-")[0].substring(0, 4);

		const parsedDate = new Date(month.toString() + "/" + day.toString() + "/" + year.toString());
		const formattedParsedDate = parsedDate.toLocaleDateString('en-US', {
			month: '2-digit',
			day: '2-digit',
			year: 'numeric',
		});

		setErrDivVisibility(false);
		setWarnDivVisiblity(false);
		Globals.IsActivationDateToday = false;

		if (formattedParsedDate > formattedCurrentDate) {
			setDate(e.target.value);
			Globals.ActivationDate = month.toString() + "/" + day.toString() + "/" + year.toString();
			console.log(Globals.ActivationDate);
			const result = log("Date: " + Globals.ActivationDate + "");

			url = '/import';

		} else if (formattedParsedDate === formattedCurrentDate) {
			setDate(e.target.value);
			Globals.ActivationDate = month.toString() + "/" + day.toString() + "/" + year.toString();
			Globals.IsActivationDateToday = true;
			const result = log("Same Date Selected: " + Globals.ActivationDate + "");
			setWarnDivVisiblity(true);

			url = '/import';


		} else {
			setDate(null);
			url = '/activation';
			setErrDivVisibility(true);
		}


	};

	const continueBtn = () => {

		const currentDate = new Date();

		const year = currentDate.getFullYear();
		const month = String(currentDate.getMonth() + 1).padStart(2, '0');
		const day = String(currentDate.getDate()).padStart(2, '0');
		const hours = String(currentDate.getHours()).padStart(2, '0');
		const minutes = String(currentDate.getMinutes()).padStart(2, '0');
		const seconds = String(currentDate.getSeconds()).padStart(2, '0');

		const formattedDateTime = month.toString() + day.toString() + year.toString() + hours.toString() + minutes.toString() + seconds.toString();

		Globals.SessionID = Globals.Username + "_" + formattedDateTime;
		console.log(Globals.SessionID)
		navigate(url);

	};

	return (
		<div>
			<h1>
				<Badge color="secondary">Select an activation date</Badge>
			</h1>

			<IdleTimerProvider
				timeout={10000 * 60}
				onIdle={onIdle}

			/>

			<Row>
				<Col md="8" lg="40">

					<Card body>

						<CardText>

							<input
								type="date"
								onChange={handleChange}
								ref={dateInputRef}
							/>

						</CardText>
					</Card>
				</Col>
			</Row>

			<p style={{ display: 'none' }} > Selected Date: {date}</p>

			<CardBody className="">

				<ButtonGroup>

					<Button onClick={() => { continueBtn(); }} disabled={date === "" || date === null} color="secondary"><b>Continue</b></Button>

				</ButtonGroup>

			</CardBody>
			<br />
			<br />
			<br />
			<br />
			<Alert style={{ display: (isErrDivVisible) ? 'block' : 'none' }} color="danger">Activation date should be in the future.</Alert>

			<Alert style={{ display: (isWarningDivVisible) ? 'block' : 'none' }} color="danger">Warning you have selected today as the Activation date! If you are sure press continue or change date.</Alert>

		</div>
	);

};

export default DatePicker;
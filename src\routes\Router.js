import { lazy } from "react";
import { Navigate } from "react-router-dom";

/****Layouts*****/
const FullLayout = lazy(() => import("../layouts/FullLayout.js"));
const LoginLayout = lazy(() => import("../layouts/LoginLayout.js"));

/***** Pages ****/

const Login = lazy(() => import("../views/ui/Login"));
const Environment = lazy(() => import("../views/ui/Environment"));
const Starter = lazy(() => import("../views/ui/Import"));
const About = lazy(() => import("../views/About.js"));
const Activation = lazy(() => import("../views/ui/Activation"));
const Store = lazy(() => import("../views/ui/Store"));
const Alerts = lazy(() => import("../views/ui/Alerts"));
const Update = lazy(() => import("../views/ui/Update"));
const Selector = lazy(() => import("../views/ui/Selector")); 
const Validation = lazy(() => import("../views/ui/Validation"));
const Badges = lazy(() => import("../views/ui/Badges"));
const Buttons = lazy(() => import("../views/ui/Alerts"));
const Cards = lazy(() => import("../views/Starter.js")); //Reports
const Report = lazy(() => import("../views/ui/Report")); //Reports
const History = lazy(() => import("../views/ui/History"));  
const Grid = lazy(() => import("../views/ui/FAQ"));      //Faqs
const Tables = lazy(() => import("../views/ui/Tables"));
const Forms = lazy(() => import("../views/ui/Forms"));
const Export = lazy(() => import("../views/ui/Export"));
const Utilities = lazy(() => import("../views/ui/Utilities"));
const Breadcrumbs = lazy(() => import("../views/ui/Breadcrumbs"));

/*****Routes******/

const ThemeRoutes = [
  {
    path: "/",
    element: <LoginLayout />,
    children: [ 
      { path: "/", element: <Navigate to="/login" />},
      { path: "/login", exact: true, element: <Login /> },

    ]
  },  
  {
    path: "/",
    element: <FullLayout />,
    children: [
      { path: "/", element: <Navigate to="/environment" /> },
      { path: "/environment", exact: true, element: <Environment /> },
	  { path: "/starter", exact: true, element: <Starter /> },	
      { path: "/import", exact: true, element: <Starter /> },	  	  
      { path: "/about", exact: true, element: <About /> },
      { path: "/alerts", exact: true, element: <Alerts /> },
	  { path: "/update", exact: true, element: <Update /> },
	  { path: "/selector", exact: true, element: <Selector /> },
	  { path: "/export", exact: true, element: <Export /> },
      { path: "/badges", exact: true, element: <Validation /> },
	  { path: "/store", exact: true, element: <Store /> },
	  { path: "/activation", exact: true, element: <Activation /> },
      { path: "/validation", exact: true, element: <Validation /> },
	  { path: "/report", exact: true, element: <Report /> },
      { path: "/buttons", exact: true, element: <Buttons /> },
      { path: "/history", exact: true, element: <History /> },
      { path: "/cards", exact: true, element: <Cards /> },
      { path: "/grid", exact: true, element: <Grid /> },
      { path: "/table", exact: true, element: <Tables /> },
      { path: "/forms", exact: true, element: <Forms /> },
      { path: "/utilities", exact: true, element: <Utilities /> },
      { path: "/breadcrumbs", exact: true, element: <Breadcrumbs /> },
    ],
  },
];

export default ThemeRoutes;



import Globals from '../../Globals';

export const loghistory = async () => {
	   
	   let response = await fetch(Globals.BaseURL+'/api/History', { 
	    method: 'POST',
		 body: 
			 
             Globals.LogHistory
		   
          ,
		  headers: {
            'Content-type': 'application/json; charset=UTF-8',
			'Accept' : 'application/json',
			'Accept-Encoding' : 'gzip,deflate',
			 
         },
	   });
	   
	   let data = await response.json();        
};
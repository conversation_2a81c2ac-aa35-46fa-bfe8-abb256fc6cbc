﻿using AMPSMiddleWare.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.MenuItem
{
    public class MenuItemFamilyStyle
    {
        public bool IsPizza { get; set; }
        public bool IsTopping { get; set; }
        public bool IsFraction { get; set; }
        public bool FractionSumsMustEqualWhole { get; set; }
        public MenuItemFractionType FractionType { get; set; }
        public MenuItemFractionPricingOverrideType FractionPricingOverride { get; set; }
    }
}

﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMPSMiddleWare.Models.History;

using System.IO;
using System.Text;
using System.Reflection;


namespace AMPSMiddleWare.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class HistoryController : ControllerBase
    {
        private string m_hisPath = string.Empty;
       

        [HttpPost]
        public IActionResult History(LogHistory logHistory)
        {

            m_hisPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) + "\\" + "history.txt";
            AppendHistoryToFile(logHistory);
            return Ok(new { Message = "History received" });
        }


        [HttpGet]
        public IActionResult getHistory()
        {

            m_hisPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) + "\\" + "history.txt";

            if (System.IO.File.Exists(m_hisPath))
            {
                string history = System.IO.File.ReadAllText(m_hisPath);
                return Ok(history);
            }
            else
            {
                return NotFound();
            }

        }


        private async Task AppendHistoryToFile(LogHistory logHistory)
        {

            string historyText = $"{logHistory.Message} \n";
            try
            {

                await System.IO.File.AppendAllTextAsync(m_hisPath, historyText, Encoding.UTF8);

            }
            catch (Exception ex)
            {

            }

        }




    }


}

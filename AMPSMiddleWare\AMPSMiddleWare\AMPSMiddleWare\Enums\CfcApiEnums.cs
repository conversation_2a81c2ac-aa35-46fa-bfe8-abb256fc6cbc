﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Enums
{
    public enum ItemCategoryType
    {
        General = 0,
        Sales = 1,
        Retail = 2
    }

    public enum MenuItemType
    {
        Standard = 0,
        CashCard = 1,
        GiftCard = 2,
        BasicGiftCertificate = 3,
        AlohaGiftCertificate = 4,
        FamilyStyle = 5,
        Build = 6,
        CampusCard = 7,
    }

    public enum MenuItemPricingMethod
    {
        Price = 0,
        PriceLevel = 1,
        QuantityPrice = 2,
        AskForPrice = 3
    }

    public enum MenuItemFractionType
    {
        Quarter = 1,
        Third = 2,
        Half = 3
    }

    public enum MenuItemFractionPricingOverrideType
    {
        None = 0,
        Percentage = 1,
        Average = 2,
        Higher = 3,
        WholePrice = 4,
        ToppingAverage = 5
    }

    public enum MenuItemSubstitutionChargeType
    {
        NoCharge = 0,
        ChargeDifference = 2
    }
}

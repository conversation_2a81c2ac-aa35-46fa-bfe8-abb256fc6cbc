﻿using AMPSMiddleWare.Models._BaseModels.BaseRequest;
using AMPSMiddleWare.Settings;
using System;
using System.Collections.Generic;
using System.Text;
 

namespace AMPSMiddleWare.Models.PriceChange
{
    public class UpdatePriceChangesReq
    {
        public UpdatePriceChangesReq(CFCSettings settings)
        {
            this.Header = new RequestHeader(settings);
        }

        public RequestHeader Header { get; set; }
        public List<PriceChangeUpdateMinimal> Batch { get; set; }
    }
}

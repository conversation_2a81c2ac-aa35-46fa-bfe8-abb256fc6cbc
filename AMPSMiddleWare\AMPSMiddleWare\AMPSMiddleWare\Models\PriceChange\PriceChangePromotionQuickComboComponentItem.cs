﻿using AMPSMiddleWare.Models.MenuItem;
using AMPSMiddleWare.Models.Promotion;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChangePromotionQuickComboComponentItem
    {
        public MenuItemReference Item { get; set; }
        public float? RegularSurcharge { get; set; }
        public PromotionReference Promotion { get; set; }
        public float? Upsell1Surcharge { get; set; }
        public float? Size1Surcharge { get; set; }
        public float? Upsell2Surcharge { get; set; }
        public float? Size2Surcharge { get; set; }
    }
}

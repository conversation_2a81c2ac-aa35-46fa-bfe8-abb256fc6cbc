﻿using AMPSMiddleWare.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.MenuItem
{
    public class MenuItemDynamicModifier
    {
        public MenuItemReference ModifierItem { get; set; }
        public bool Included { get; set; }
        public bool AutoAdd { get; set; }
        public MenuItemSubstitutionChargeType? SubstitutionCharge { get; set; }
    }
}

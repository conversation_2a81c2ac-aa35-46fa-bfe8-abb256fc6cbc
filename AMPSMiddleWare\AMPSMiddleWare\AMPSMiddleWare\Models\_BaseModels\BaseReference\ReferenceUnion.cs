﻿using AMPSMiddleWare.Models.ActivationSchedule;
 
using AMPSMiddleWare.Models.Menu;
using AMPSMiddleWare.Models.MenuItem;
 
using AMPSMiddleWare.Models.Owner;
using AMPSMiddleWare.Models.PriceChange;
using AMPSMiddleWare.Models.PriceLevel; 
using AMPSMiddleWare.Models.Store;
 
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models._BaseModels.BaseReference
{
    public class ReferenceUnion
    {
        public ActivationScheduleReference ActivationSchedule { get; set; }
       
        public MenuItemReference MenuItem { get; set; }
        public MenuReference Menu { get; set; }
        
        public OwnerReference Owner { get; set; }
        
        public PriceChangeReference PriceChange { get; set; }
        public PriceLevelReference PriceLevel { get; set; }
        
        public StoreReference Store { get; set; }
         
    }
}

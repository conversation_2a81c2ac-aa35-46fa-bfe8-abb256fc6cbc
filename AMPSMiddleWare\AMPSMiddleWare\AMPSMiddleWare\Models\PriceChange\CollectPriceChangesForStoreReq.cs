﻿using AMPSMiddleWare.Models._BaseModels.BaseRequest;
using AMPSMiddleWare.Settings;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class CollectPriceChangesForStoreReq
    {
        public CollectPriceChangesForStoreReq(CFCSettings settings)
        {
            this.Header = new RequestHeader(settings);
        }

        public RequestHeader Header { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public string StoreUniqueID { get; set; }
    }
}

# Frontend Deployment Script for MenuOps Pricing Tool
# Usage: .\deploy-frontend.ps1 -Environment "production" -TargetPath "C:\inetpub\wwwroot\menuops"

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("development", "production", "qa")]
    [string]$Environment,

    [Parameter(Mandatory=$true)]
    [string]$TargetPath,

    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Host "=== MenuOps Pricing Tool - Frontend Deployment ===" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Target Path: $TargetPath" -ForegroundColor Yellow
Write-Host "Project Root: $ProjectRoot" -ForegroundColor Yellow

try {
    # Change to project root
    Set-Location $ProjectRoot

    # Check if Node.js is installed
    Write-Host "Checking Node.js installation..." -ForegroundColor Cyan
    $nodeVersion = node --version 2>$null
    if (-not $nodeVersion) {
        throw "Node.js is not installed or not in PATH"
    }
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green

    # Check if npm is available
    $npmVersion = npm --version 2>$null
    if (-not $npmVersion) {
        throw "npm is not available"
    }
    Write-Host "npm version: $npmVersion" -ForegroundColor Green

    # Install dependencies if node_modules doesn't exist
    if (-not (Test-Path "node_modules")) {
        Write-Host "Installing npm dependencies..." -ForegroundColor Cyan
        npm install
        if ($LASTEXITCODE -ne 0) {
            throw "npm install failed"
        }
    }

    # Build the application if not skipped
    if (-not $SkipBuild) {
        Write-Host "Building React application for $Environment..." -ForegroundColor Cyan

        switch ($Environment) {
            "development" { npm run build }
            "production" { npm run build-prod }
            "qa" { npm run build-qa }
        }

        if ($LASTEXITCODE -ne 0) {
            throw "Build failed for environment: $Environment"
        }

        Write-Host "Build completed successfully!" -ForegroundColor Green
    }

    # Check if build directory exists
    $BuildPath = Join-Path $ProjectRoot "build"
    if (-not (Test-Path $BuildPath)) {
        throw "Build directory not found at: $BuildPath"
    }

    # Create target directory if it doesn't exist
    if (-not (Test-Path $TargetPath)) {
        Write-Host "Creating target directory: $TargetPath" -ForegroundColor Cyan
        New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
    }

    # Backup existing deployment if it exists
    $BackupPath = "$TargetPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    if (Test-Path $TargetPath) {
        $existingFiles = Get-ChildItem $TargetPath -ErrorAction SilentlyContinue
        if ($existingFiles) {
            Write-Host "Creating backup at: $BackupPath" -ForegroundColor Cyan
            Copy-Item -Path $TargetPath -Destination $BackupPath -Recurse -Force
        }
    }

    # Clear target directory
    Write-Host "Clearing target directory..." -ForegroundColor Cyan
    Get-ChildItem $TargetPath -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue

    # Copy build files to target
    Write-Host "Copying build files to target..." -ForegroundColor Cyan
    Copy-Item -Path "$BuildPath\*" -Destination $TargetPath -Recurse -Force

    # Verify deployment
    $indexFile = Join-Path $TargetPath "index.html"
    if (-not (Test-Path $indexFile)) {
        throw "Deployment verification failed: index.html not found"
    }

    Write-Host "=== Deployment Completed Successfully! ===" -ForegroundColor Green
    Write-Host "Files deployed to: $TargetPath" -ForegroundColor Yellow
    Write-Host "Backup created at: $BackupPath" -ForegroundColor Yellow

    # Display deployment summary
    $deployedFiles = Get-ChildItem $TargetPath -Recurse -File
    Write-Host "Total files deployed: $($deployedFiles.Count)" -ForegroundColor Green

} catch {
    Write-Host "=== Deployment Failed! ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Return to original location
    Set-Location $ScriptDir
}

Write-Host "Frontend deployment script completed." -ForegroundColor Green
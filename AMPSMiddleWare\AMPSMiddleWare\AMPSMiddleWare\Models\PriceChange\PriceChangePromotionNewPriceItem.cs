﻿using AMPSMiddleWare.Models.MenuItem;
using AMPSMiddleWare.Models.Promotion;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChangePromotionNewPriceItem
    {
        public PromotionReference Promotion { get; set; }
        public float? Price { get; set; }
        public MenuItemReference Item { get; set; }
    }
}

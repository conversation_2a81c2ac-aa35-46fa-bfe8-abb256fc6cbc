import React, { useRef, useState } from "react";
import * as XLSX from "xlsx";
import { <PERSON>ert, Badge, Button, Card, CardBody, CardTitle, CardText, Row, Col, UncontrolledDropdown, DropdownToggle, DropdownMenu, DropdownItem, Dropdown, ButtonGroup } from "reactstrap";
import { Link } from 'react-router-dom'
import { useNavigate } from "react-router-dom";
import Globals from '../../Globals';
import LoadingAnim from "../../assets/images/logos/loading2.gif";
import { v4 as uuid4 } from 'uuid';
import { log } from './Logger';
import { IdleTimerProvider, useIdleTimerContext } from 'react-idle-timer/dist/index.legacy.esm.js'

import jsPDF from "jspdf";
import "jspdf-autotable";
import HeaderConfigs from './UiConfig.js';
import { format } from "date-fns";

const fileTypes = ["XLSX"];

var blnErrFound = false;
var url = '/import';

class ExcelToJson extends React.Component {

	constructor(props) {
		super(props);
		this.handleClick = this.handleClick.bind(this);
		this.state = {
			file: "",
			isloadingDivVisible: false,
			isErrDivVisible: false,
			isPriceLevel: false,
			isHasPriceLevel: false,
			isValidationComplete: false,
			isSameDayUpdate: false,
			alertArry: [],
		};

		if (!Globals.Defs) {
			Globals.Defs = {};
		}

		if (!Globals.Defs.PriceChangeVersionType) {
			Globals.Defs.PriceChangeVersionType = Object.freeze({
				INVALID: Symbol("invalid"),
				GLOBAL: Symbol("global"),
				CORPORATE: Symbol("corporate"),
				STOREGROUP: Symbol("storegroup"),
				STORE: Symbol("store")
			});
		}

		if (!Globals.Defs.ActionType) {
			Globals.Defs.ActionType = Object.freeze({
				INVALID: Symbol("invalid"),
				CREATE: Symbol("create"),
				UPDATE: Symbol("update"),
				NOACTION: Symbol("noaction")
			});
		}

	}

	// Alert types for error text
	AlertTypes = {

		ERR_TEMPLATE: "Template error",
		ERR_ACTIVATION: "Activation date error",
		ERR_STORE: "Store not found error",
		ERR_OWNER: "Owner not found error"

	};

	// Header for simple API POST communication
	APIHeaderPOST = {
		method: 'POST',
		body:
			"",
		headers: {
			'Content-type': 'application/json; charset=UTF-8',
			'Accept': 'application/json',
			'Accept-Encoding': 'gzip,deflate',
		},
	}

	getCFCHeader = () => {
		var jsonCFCHeader = '{ "Header" ';
		jsonCFCHeader += ":";
		jsonCFCHeader += "{";
		jsonCFCHeader += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
		jsonCFCHeader += ' "ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
		jsonCFCHeader += ' "ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
		jsonCFCHeader += ' "MessageID"' + " : " + HeaderConfigs.MessageID;
		jsonCFCHeader += " },";
		return jsonCFCHeader;
	}

	toggleDiv = () => {
		this.setState((prevState) => ({
			isloadingDivVisible: !prevState.isloadingDivVisible,
		}));
	}

	toggleErrDiv = () => {
		this.setState((prevState) => ({
			isErrDivVisible: !prevState.isErrDivVisible,
		}));
	}

	toggleHasDiv = () => {
		this.setState((prevState) => ({
			isHasPriceLevel: !prevState.isHasPriceLevel,
		}));
	}

	setSameDayUpdate = (bSet) => {
		this.setState({ isSameDayUpdate: bSet });
	}

	toggleValidationComplete = () => {
		this.setState((prevState) => ({
			isValidationComplete: !prevState.isValidationComplete,
		}));
	}

	setAlertText = (atext) => {
		var found = this.state.alertArry.includes(atext);
		if (!found) {
			this.state.alertArry = this.state.alertArry.concat([atext]);
		}
	}

	resetAlertStatus() {
		this.state.isValidationComplete = false;
		this.state.isErrDivVisible = false;
		Globals.TemplateErr = "";
		this.state.alertArry.length = 0;
	}


	handleClick(e) {
		this.refs.fileUploader.click();
		this.refs.fileUploader.fileTypes = "*.xlsx";
	}

	filePathset(e) {
		this.resetAlertStatus();
		e.stopPropagation();
		e.preventDefault();
		var file = e.target.files[0];


		//Globals.LogEntry = '{  "logLevel": "info",  "message": "File"   }';  
		//const result = log();  	 




		this.setState({ file });

		if (file !== undefined) {
			Globals.TemplateFileName = file.name;
			//Globals.LogEntry = '{  "logLevel": "info",  "message": "FileName: ' +  Globals.TemplateFileName   + '"   }';
			const result2 = log("FileName: " + Globals.TemplateFileName + "");
		}

	}

	getMenuItems = async () => {

		let response = await fetch(Globals.BaseURL + '/ItemList', this.APIHeaderPOST);

		blnErrFound = false;
		Globals.TemplateErr = "";
		let data = await response.json();

		Globals.ItemList = data.list;

		// Globals.LogEntry = '{  "logLevel": "info",  "message": "Items"   }';    
		// Globals.LogEntry = '{  "logLevel": "info",  "message": "' +  Globals.ItemList.toString()  + '"   }'; 	   
		//const result = log("Get Menu Items");
		//const result2 = log("" +  Globals.ItemList.toString()  + "" ); 
		//this.getPriceChanges();
		this.getStores();
	};

	GenerateErrReport = () => {

		const doc = new jsPDF();

		var image = "data:image/png;base64,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";
		doc.addImage(image, 'PNG', 3, -10, 50, 50);

		const date = Date().split(" ");

		// we use a date string to generate our filename.
		const dateStr = date[0] + date[1] + date[2] + date[3] + date[4];

		doc.setFontSize(23);

		// get width of page for centering
		const width = doc.internal.pageSize.getWidth();

		// we define the name of our PDF file.
		doc.text("CFC Error Report", width / 2, 18, { align: 'center' });

		// set the text for top information
		doc.setFontSize(18);
		doc.text("Selected Customer :: " + Globals.SelectedCustomer, 14, 45);
		doc.text("Selected Environment :: " + Globals.SelectedEnvironment, 14, 55);
		doc.text("Activation Date :: " + Globals.ActivationDate, 14, 65);
		doc.text("User :: " + Globals.Username, 14, 75);


		// set text for template name
		doc.setFontSize(12)
		doc.text("The following error(s) has been found in the template: ", 14, 95).setFont(undefined, 'bold');
		doc.text(Globals.TemplateFileName, 14, 102).setFont(undefined, 'normal');

		doc.text("Please fix the issues and try reuploading.", 14, 112);

		// set errors text to display
		doc.setFontSize(12)
		doc.text(Globals.TemplateErr, 14, 125);

		// save the file
		doc.save(`CFC_Err_report_${dateStr}.pdf`);

		const result = log("Error has occured. Generating report.", "warn");

	}

	getPriceChanges = async () => {

		let response = await fetch(Globals.BaseURL + '/GetPriceChanges', this.APIHeaderPOST);

		let data = await response.json();
		console.log("price change list ");
		console.log(data.list);
		Globals.Pricechanges = data.list;

		this.getPromotions();
	};

	getPromotions = async () => {

		let response = await fetch(Globals.BaseURL + '/getPromotions', this.APIHeaderPOST);

		let data = await response.json();
		console.log("promotions list ");
		console.log(data.list);
		Globals.Promotions = data.list;

		this.getStores();
	};

	getStores = async () => {

		let response = await fetch(Globals.BaseURL + '/StoreList', this.APIHeaderPOST);

		let data = await response.json();
		console.log("stores ");
		Globals.Stores = data.list;
		console.log(Globals.Stores);

		let storePosID = null;

		var myObject = JSON.parse(Globals.TemplateData);
		for (let i = 0; i < myObject.length; i++) {
			//Globals.POSStartDate = myObject[i].StartDate; 
			try {
				Globals.POSNumber = myObject[i].SiteName;
				var TemplateHeaderCheckStartDate = myObject[i].StartDate;
				console.log(myObject[i].StartDate);
				var TemplateHeaderCheckItemName = myObject[i].ItemName;
				var TemplateHeaderCheckPrice = myObject[i].AlaCartePrice;
				var TemplateHeaderCheckPOSNumber = myObject[i].PosNumber;

				if (Globals.POSNumber == undefined || TemplateHeaderCheckPOSNumber == undefined || TemplateHeaderCheckStartDate == undefined || TemplateHeaderCheckItemName == undefined || TemplateHeaderCheckPrice == undefined) {
					blnErrFound = true;
					this.setAlertText(this.AlertTypes.ERR_TEMPLATE);
					Globals.TemplateErr = "Invalid Template Headers found. Please contact support team";
					console.log(Globals.TemplateErr);
					this.toggleDiv();
					this.toggleErrDiv();
					this.GenerateErrReport();
					return;

				}

				if (new Date(Globals.ActivationDate).toLocaleString() != new Date(myObject[i].StartDate).toLocaleString()) {
					blnErrFound = true;
					this.setAlertText(this.AlertTypes.ERR_ACTIVATION);
					if (Globals.TemplateErr == "") {
						Globals.TemplateErr = "Template StartDate " + myObject[i].StartDate + " has to match the activation date " + myObject[i].ItemName;
					} else {
						Globals.TemplateErr = Globals.TemplateErr + '\n' + "Template StartDate " + myObject[i].StartDate + " has to match the activation date " + myObject[i].ItemName;
					}
				}

				if (!storePosID) {
					const store = Globals.Stores.find((store) => store.posNumber === parseInt(myObject[i].SiteName));
					if (store) {
						Globals.storeUniqueID = store.storeUniqueID;
						storePosID = parseInt(myObject[i].SiteName);
						//await this.getTableDefinitions();
					} else {
						if (Globals.TemplateErr == "") {
							Globals.TemplateErr = "Store not found " + myObject[i].SiteName;
						} else {
							Globals.TemplateErr = Globals.TemplateErr + '\n' + "Store not found " + myObject[i].SiteName;
						}
						blnErrFound = true;
						this.setAlertText(this.AlertTypes.ERR_STORE);
						console.log("Store not found " + myObject[i].SiteName);
					}

				} else {

					if ((myObject[i].SiteName) && !(storePosID === parseInt(myObject[i].SiteName))) {
						blnErrFound = true;
						if (Globals.TemplateErr == "") {
							Globals.TemplateErr = "More than one storeName provided " + myObject[i].SiteName;
						} else {
							Globals.TemplateErr = Globals.TemplateErr + '\n' + "More than one storeName provided " + myObject[i].SiteName;
						}
						this.setAlertText(this.AlertTypes.ERR_TEMPLATE);

					}
				}



				//const owner =  Globals.Owners.find( (owner) => owner.posNumber === parseInt(myObject[i].SiteName));
			} catch (error) {
				blnErrFound = true;

				Globals.TemplateErr = "Invalid Template Headers found. Please contact support team";
				this.toggleErrDiv();
				this.GenerateErrReport();
			}
		}

		await this.getStoreDetails();

		this.getOwners();
	};

	getOwners = async () => {

		let response = await fetch(Globals.BaseURL + '/OwnersList', this.APIHeaderPOST);

		let data = await response.json();
		console.log("owners");
		console.log(data.list);
		Globals.Owners = data.list;

		const owner = Globals.Owners.find((owner) => owner.name === "Global"); //Global
		if (owner) {
			console.log("owner.ownerUniqueID");
			console.log(owner.ownerUniqueID);
			Globals.ownerUniqueID = owner.ownerUniqueID;
		} else {
			console.log("Owner not found ");
			Globals.TemplateErr = Globals.TemplateErr + '\n' + "Owner Global not found ";
			blnErrFound = true;
			this.setAlertText(this.AlertTypes.ERR_OWNER);

		}

		// Check for error before moving on. 
		if (!blnErrFound) {
			this.getStorePriceChangeSchedule();

		} else {

			this.toggleDiv();
			this.toggleErrDiv();
			this.GenerateErrReport();
			return;
		}


	};

	getPriceLevelData = async () => {

		var jsonPromoItemTxt = this.getCFCHeader();
		jsonPromoItemTxt += '"StoreUniqueID"' + " : " + '"' + Globals.storeUniqueID + '"'
		jsonPromoItemTxt += " }";

		let response = await fetch(Globals.BaseURL + '/CollectPriceLevelsForStore', {
			method: 'POST',
			body:

				jsonPromoItemTxt

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',

			},
		});

		let data = await response.json();

		Globals.PriceLevel = data.list;

		this.PriceLevelLookup();

	};

	PriceLevelLookup = () => {

		var retJson = '"PriceLevelAssignments"' + " : " + " [ ";

		var myObject = JSON.parse(Globals.TemplateDataPriceLevel);

		for (let i = 0; i < myObject.length; i++) {

			const item = Globals.PriceLevel.find((item) => item.posNumber.toString() === myObject[i].PosNumber);
			if (item) {
				if (myObject[i].PriceLevel.toString().length > 0) {
					console.log("PriceLevel item found:" + myObject[i].ItemName);

					var txt = this.BuildPriceLevelReturn(item, myObject[i].PriceLevel);

					retJson += txt;
				}
				//  console.log(txt);

			} else {
				// console.log("PriceLevel item not found " + myObject[i].ItemName);
			}
		}

		retJson = retJson.slice(0, -1); // Remove trailing comma
		retJson += " ] ";

		Globals.PriceLevelChange = retJson;

		console.log("PriceLevel Return JSON:" + retJson);
		this.determineGlobalPrice();

	};

	BuildPriceLevelReturn = (item, price) => {

		if (item) {

			var jsonTxt = " { ";

			jsonTxt += '"PriceLevel"' + " : " + " { ";
			jsonTxt += '"PriceLevelUniqueID"' + " : " + '"' + item.priceLevelUniqueID.toString() + '"' + " , ";
			jsonTxt += '"PriceLevelID"' + " : " + '"' + " " + '"' + " , ";
			jsonTxt += '"PriceLevelName"' + " : " + '"' + item.name + '"' + " , ";
			jsonTxt += '"PriceLevelPOSNumber"' + " : " + '"' + item.posNumber + '"' + " , ";
			jsonTxt += '"OwnerUniqueID"' + " : " + '"' + item.owner.ownerUniqueID.toString() + '"' + " , ";
			jsonTxt += '"CorporatePOSNumber"' + " : " + '"' + Globals.corporatePOSNumber + '"' + " , ";
			jsonTxt += '"CorporateName"' + " : " + '"' + Globals.corporateName + '"' + ",";
			jsonTxt += '"StoreName"' + " : " + '"' + Globals.storeName + '"';
			//jsonTxt += '"StoreGroupName"' + " : " + '"' + Globals.storeGroupName + '"';
			jsonTxt += "},";
			jsonTxt += '"Price"' + " : " + '"' + price + '"';
			jsonTxt += "}";
			jsonTxt += ","

		}

		return jsonTxt;

	};



	getTableDefinitions = async () => {

		var jsonTblDefTxt = '{ "Header" ';
		jsonTblDefTxt += ":";
		jsonTblDefTxt += "{";
		jsonTblDefTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
		jsonTblDefTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
		jsonTblDefTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
		jsonTblDefTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID + " , ";
		jsonTblDefTxt += '"CorrelationID"' + " : " + '"PriceChange"' + " , ";
		jsonTblDefTxt += '"Accept"' + " : " + '"application/json"' + " , ";
		jsonTblDefTxt += '"Connection"' + " : " + '"keep-alive"';

		jsonTblDefTxt += " },";
		jsonTblDefTxt += '"StoreUniqueID"' + " : " + '"' + Globals.storeUniqueID + '"';
		jsonTblDefTxt += " }";

		let response = await fetch(Globals.BaseURL + '/TableDefinitions', {
			method: 'POST',
			body:

				jsonTblDefTxt

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',

			},
		});


		let data = await response.json();

		console.log("TableDefinitions");
		Globals.corporatePOSNumber = data.store.corporatePOSNumber;
		Globals.corporateName = data.store.corporateName;
		console.log(Globals.corporatePOSNumber);
		console.log(Globals.corporateName);


		//    const globalPricing =  Globals.Pricechanges.find( (globalPricing) => globalPricing.name === "MPT_GlobalVersion");
		// 	if(globalPricing){	

		// 		console.log("globalPricing found.");
		// 		Globals.GlobalPricingExists = true;
		// 		Globals.GlobalPricingRecordID = globalPricing.priceChangeUniqueID;			 
		// 		this.createPriceChangeReq();
		// 	}else{
		// 		console.log("globalPricing not found. Creating one");	
		// 		Globals.GlobalPricingExists = false;
		// 		this.createGlobalPriceChangeReq();
		// 	}	

		if (this.state.isPriceLevel) {
			this.getPriceLevelData();
		} else {
			this.determineGlobalPrice();
		}

	};

	determineGlobalPrice() {


		this.createPriceChangeReq();
		/*
				const globalPricing = Globals.Pricechanges.find((globalPricing) => globalPricing.name === "MPT_GlobalVersion");
				if (globalPricing) {
		
					console.log("globalPricing found.");
					Globals.GlobalPricingExists = true;
					Globals.GlobalPricingRecordID = globalPricing.priceChangeUniqueID;
					this.createPriceChangeReq();
				} else {
					console.log("globalPricing not found. Creating one");
					Globals.GlobalPricingExists = false;
					this.createGlobalPriceChangeReq();
				}*/

	}

	getStoreDetails = async () => {
		var jsonGetStoresTxt = this.getCFCHeader();
		jsonGetStoresTxt += '"References"' + " : [ {";
		jsonGetStoresTxt += '"StoreUniqueID"' + " : " + '"' + Globals.storeUniqueID + '"';
		jsonGetStoresTxt += "} ]";
		jsonGetStoresTxt += " }";

		console.log("getStoreDetails Request: ");
		console.log(jsonGetStoresTxt);

		let response = await fetch(Globals.BaseURL + '/GetStores', {
			method: 'POST',
			body:

				jsonGetStoresTxt

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',
			},
		});

		let data = await response.json();

		console.log("getStoreDetails Responce: ");

		let storeDetails = JSON.stringify(data.List);
		console.log(storeDetails);


		var myObject = data.List; //JSON.parse(storeDetails);


		try {
			for (let i = 0; i < myObject.length; i++) {
				if (myObject[i].StoreUniqueID == Globals.storeUniqueID) {
					Globals.corporatePOSNumber = myObject[i].ParentOwner.CorporatePOSNumber;
					Globals.corporateName = myObject[i].ParentOwner.CorporateName;
					Globals.corporateUniqueID = myObject[i].ParentOwner.OwnerUniqueID;
					Globals.storeName = myObject[i].Name;
				}
			}

		} catch (error) { }

		if (Globals.corporateUniqueID === "") {
			// Error, could not find Corporate details
			console.log("Error: Could not find store details.");
		}


		//console.log(Globals.corporateUniqueID);
		//console.log(Globals.corporateName);
		//console.log(Globals.corporatePOSNumber);


	}

	getStorePriceChangeSchedule = async () => {


		const startDate = new Date(Globals.ActivationDate);

		var jsonPriceChangeTxt = this.getCFCHeader();

		jsonPriceChangeTxt += '"EffectiveDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
		jsonPriceChangeTxt += '"StoreUniqueID"' + " : " + '"' + Globals.storeUniqueID + '"'
		jsonPriceChangeTxt += " }";

		let response = await fetch(Globals.BaseURL + '/CollectPriceChangesForStore', {
			method: 'POST',
			body:

				jsonPriceChangeTxt

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',

			},
		});

		let data = await response.json();

		let requestedPRNumber = null;
		Globals.ValidStorePriceChange = null;

		var templateObject = JSON.parse(Globals.TemplateData);
		for (let i = 0; i < templateObject.length; i++) {
			if ((templateObject[i].GlobalPRnumber)) {
				requestedPRNumber = templateObject[i].GlobalPRnumber;
				break;
			}
		}

		if (requestedPRNumber === null || !requestedPRNumber) {
			blnErrFound = true;
			if (Globals.TemplateErr == "") {
				Globals.TemplateErr = "Invalid Template file, Global Price Change number not provided. " + "\n";
			} else {
				Globals.TemplateErr = Globals.TemplateErr + '\n' + "Invalid Template file, Global Price Change number not provided. " + "\n";
			}

		}

		Globals.ValidPriceChangeVersion = Globals.Defs.PriceChangeVersionType.INVALID;

		var myObject = data.List; //JSON.parse(Globals.StorePriceChanges);

		try {
			for (let i = 0; i < myObject.length; i++) {

				if (myObject[i].POSNumber == requestedPRNumber) {
					Globals.ValidStorePriceChange = myObject[i];

					Globals.GlobalPriceChangeID = myObject[i].PriceChangeID;
					Globals.GlobalPricingRecordID = myObject[i].PriceChangeUniqueID;
					Globals.GlobalPricingRecordName = myObject[i].Name;
					Globals.GlobalPricingRecordPOSNumber = myObject[i].POSNumber;
					Globals.GlobalPriceChangeStartDate = myObject[i].StartDate;


					if (!(myObject[i].StartDate) || (myObject[i].Owner.OwnerUniqueID === Globals.ownerUniqueID)) {
						Globals.ValidPriceChangeVersion = Globals.Defs.PriceChangeVersionType.GLOBAL;
					}
					else if (myObject[i].Owner.OwnerUniqueID === Globals.corporateUniqueID) {
						Globals.ValidPriceChangeVersion = Globals.Defs.PriceChangeVersionType.CORPORATE;
					} else if (myObject[i].Owner.OwnerUniqueID === Globals.storeUniqueID) {
						Globals.ValidPriceChangeVersion = Globals.Defs.PriceChangeVersionType.STORE;
					} else if (myObject[i].Owner.StoreGroupName) {
						Globals.ValidPriceChangeVersion = Globals.Defs.PriceChangeVersionType.STOREGROUP;
						Globals.StoreGroupName = myObject[i].Owner.StoreGroupName;
						Globals.StoreGroupUniqueID = myObject[i].Owner.OwnerUniqueID;
					}


					if (new Date(Globals.ActivationDate).toLocaleString() == new Date(myObject[i].StartDate).toLocaleString()) {
						Globals.IsPriceChangeStartDateIsActivationDate = true;
					}

				}

			}
		} catch (error) {
			console.log(error);
			blnErrFound = true;
			if (Globals.TemplateErr == "") {
				Globals.TemplateErr = "Error while processing Price Changes for store " + "\n";
			} else {
				Globals.TemplateErr = Globals.TemplateErr + "Error while processing Price Changes for store " + "\n";
			}
		}


		if (Globals.ValidPriceChangeVersion === Globals.Defs.PriceChangeVersionType.INVALID) {
			blnErrFound = true;
			if (Globals.TemplateErr == "") {
				Globals.TemplateErr = "Failed to find Price Change Version " + "\n";
			} else {
				Globals.TemplateErr = Globals.TemplateErr + "Failed to find Price Change Version " + "\n";
			}
		}

		console.log("Price Change Version: ");
		console.log(Globals.ValidPriceChangeVersion);

		console.log("IsPriceChangeStartDateIsActivationDate: ");
		console.log(Globals.IsPriceChangeStartDateIsActivationDate);

		console.log("IsActivationDateToday: ");
		console.log(Globals.IsActivationDateToday);

		console.log("StoreGroupName: ");
		console.log(Globals.StoreGroupName);

		console.log("StoreGroupUniqueID: ");
		console.log(Globals.StoreGroupUniqueID);

		console.log("GlobalPricingRecordID: ");
		console.log(Globals.GlobalPricingRecordID);

		console.log("Globals.GlobalPriceChangeStartDate: " + Globals.GlobalPriceChangeStartDate);

		console.log("ownerUniqueID: ");
		console.log(Globals.ownerUniqueID);

		console.log("ValidStorePriceChange: ");
		console.log(Globals.ValidStorePriceChange);

		//this.getTableDefinitions();

		if (blnErrFound) {
			this.toggleDiv();
			this.toggleErrDiv();
			this.GenerateErrReport();
		} else {
			url = '/selector';

			this.toggleDiv();
			this.toggleValidationComplete();
		}

	};


	createGlobalPriceChangeReq() {

		console.log("createGlobalPriceChangeReq");

		var myObject = JSON.parse(Globals.TemplateData);
		const guid = uuid4();
		Globals.GlobalPricingRecordID = guid;
		const currentDate = new Date();

		const year = currentDate.getFullYear();
		const month = String(currentDate.getMonth() + 1).padStart(2, '0');
		const day = String(currentDate.getDate()).padStart(2, '0');
		const hours = String(currentDate.getHours()).padStart(2, '0');
		const minutes = String(currentDate.getMinutes()).padStart(2, '0');
		const seconds = String(currentDate.getSeconds()).padStart(2, '0');

		const formattedDateTime = month.toString() + day.toString() + year.toString() + hours.toString() + minutes.toString() + seconds.toString();

		const startDate = new Date(Globals.ActivationDate);
		console.log(Globals.ActivationDate)
		//const endDate =  startDate.setFullYear(startDate.getFullYear() + 30); 
		const endDate = null;

		console.log(startDate)
		console.log(endDate)


		var jsonTxt = '{ "Header" ';
		jsonTxt += ":";
		jsonTxt += "{";
		jsonTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
		jsonTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
		jsonTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
		jsonTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID + " , ";
		jsonTxt += '"CorrelationID"' + " : " + '"PriceChange"' + " , ";
		jsonTxt += '"Accept"' + " : " + '"application/json"' + " , ";
		jsonTxt += '"Connection"' + " : " + '"keep-alive"';

		jsonTxt += " },";
		jsonTxt += '"Batch"';
		jsonTxt += " : ";
		jsonTxt += " [ ";
		jsonTxt += " { ";

		jsonTxt += '"PriceChangeUniqueID"' + " : " + '"' + guid + '"' + " , ";
		jsonTxt += '"PriceChangeID"' + " : " + '"MPT_Pricing"' + " , ";
		jsonTxt += '"Owner"' + " : " + " { ";
		jsonTxt += '"OwnerUniqueID"' + " : " + '"' + Globals.ownerUniqueID + '"';
		jsonTxt += " },";
		//jsonTxt += '"StartDate"' + " : " + '"'  +  new Date(Globals.POSStartDate).toISOString() + '"' + " , " ;
		//jsonTxt += '"EndDate"' + " : "  +   '"'  +  new Date(endDate).toISOString()  + '"' +  " , " ;
		//jsonTxt += '"EndDate"' + " : "  +  '"2050-01-01T00:00:00.00"' +  " , " ;
		jsonTxt += '"POSStartDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
		jsonTxt += '"POSEndDate"' + " : " + '"2050-01-01T00:00:00.00"' + " , ";

		jsonTxt += '"Name"' + " : " + '"MPT_GlobalVersion"' + " , ";
		jsonTxt += '"POSNumber"' + " : " + '"' + 5 + '"'; // + " , " ;
		//jsonTxt += '"CorporatePOSNumber"' + " : "  +   '"' + Globals.corporatePOSNumber + '"' + " , " ;
		//jsonTxt += '"CorporateName"' + " : "  +   '"' + Globals.corporateName + '"' + " , ";
		/*
		jsonTxt += '"MenuItemAssignments"' + " : " + " [ " ;
	    
	    
		 for (let i = 0; i < myObject.length; i++) {
			 
		 	
			 const item = Globals.ItemList.find( (item) => item.name === myObject[i].ItemName);
			  
			 if(item){	
				 jsonTxt += " { ";		   
				 jsonTxt += '"MenuItem"' + " : " + " { " ;
				 jsonTxt += '"MenuItemUniqueID"' + " : " + '"' + item.menuItemUniqueID + '"' + " , " ;
				 jsonTxt += '"MenuItemName"' + " : " + '"' + item.name + '"' + " , " ;
				 jsonTxt += '"MenuItemPOSNumber"' + " : " + '"' + item.posNumber + '"' + " , " ;
				 jsonTxt += '"OwnerUniqueID"' + " : " + '"' +  Globals.storeUniqueID.toString() + '"' //+ " , ";
				 //jsonTxt += '"OwnerUniqueID"' + " : " + '"' +  Globals.ownerUniqueID.toString() + '"' + " , ";
				 //jsonTxt += '"CorporatePOSNumber"' + " : "  +   '"' + Globals.corporatePOSNumber + '"' + " , " ;
				 //jsonTxt += '"CorporateName"' + " : "  +   '"' + Globals.corporateName + '"' ;
				 jsonTxt += "}," ;
				 jsonTxt += '"Price"' + " : " +  '"' + myObject[i].AlaCartePrice + '"' ;
				 jsonTxt += "}" ;
			 	
				 if(i < myObject.length - 1){
					 jsonTxt += ","
				 }
			 	
			 }else{
				 console.log("Item not found " + myObject[i].ItemName);	
			 }
			  
			  
		 }	
	 jsonTxt +=  " ] "
	 */

		jsonTxt += " } ] } ";
		Globals.JSONGlobalPriceChangeReq = jsonTxt;
		console.log((jsonTxt));

		this.createPriceChangeReq();
	}

	createPriceChangeReq() {

		console.log("createPriceChangeReq");

		var myObject = JSON.parse(Globals.TemplateData);
		const guid = uuid4();
		//Globals.GlobalPricingRecordID  = guid;
		const currentDate = new Date();

		const year = currentDate.getFullYear();
		const month = String(currentDate.getMonth() + 1).padStart(2, '0');
		const day = String(currentDate.getDate()).padStart(2, '0');
		const hours = String(currentDate.getHours()).padStart(2, '0');
		const minutes = String(currentDate.getMinutes()).padStart(2, '0');
		const seconds = String(currentDate.getSeconds()).padStart(2, '0');

		const formattedDateTime = month.toString() + day.toString() + year.toString() + hours.toString() + minutes.toString() + seconds.toString();
		const formattedDate = month.toString() + day.toString() + year.toString().substring(2, 4) + hours.toString() + minutes.toString();

		const startDate = new Date(Globals.ActivationDate);
		const endDate = null; //startDate.setFullYear(startDate.getFullYear() + 30); 


		//console.log(formattedDateTime);

		var jsonTxt = '{ "Header" ';
		jsonTxt += ":";
		jsonTxt += "{";
		jsonTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
		jsonTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
		jsonTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
		jsonTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID + " , ";
		jsonTxt += '"CorrelationID"' + " : " + '"PriceChange"' + " , ";
		jsonTxt += '"Accept"' + " : " + '"application/json"' + " , ";
		jsonTxt += '"Connection"' + " : " + '"keep-alive"';

		jsonTxt += " },";
		jsonTxt += '"Batch"';
		jsonTxt += " : ";
		jsonTxt += " [ ";
		jsonTxt += " { ";
		jsonTxt += '"PriceChangeUniqueID"' + " : " + '"' + Globals.GlobalPricingRecordID + '"' + " , ";
		//jsonTxt += '"PriceChangeUniqueID"' + " : " + '"'  + guid + '"' + " , " ;
		//jsonTxt += '"PriceChangeUniqueID"' + " : " + '"5d1eeb19-ea1a-421f-b022-535ae0e28f9e"' + " , "; 	
		jsonTxt += '"PriceChangeID"' + " : " + '"MPT_Pricing"' + " , ";
		jsonTxt += '"Owner"' + " : " + " { ";
		jsonTxt += '"OwnerUniqueID"' + " : " + '"' + Globals.storeUniqueID + '"';
		jsonTxt += " },";
		jsonTxt += '"StartDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
		//jsonTxt += '"EndDate"' + " : "  +   '"'  +     + '"' +  " , " ;
		jsonTxt += '"EndDate"' + " : " + '"2050-01-01T00:00:00.00"' + " , ";
		jsonTxt += '"POSStartDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
		//jsonTxt += '"POSEndDate"' + " : "  +   '"'  +    + '"' +  " , " ;

		jsonTxt += '"Name"' + " : " + '"PT' + formattedDate + '_' + Globals.Username.toString().slice(0, 7) + '"' + " , ";
		jsonTxt += '"POSNumber"' + " : " + '"' + 5 + '"' + " , ";
		//jsonTxt += '"CorporatePOSNumber"' + " : "  +   '"' + Globals.corporatePOSNumber + '"' + " , " ;
		//jsonTxt += '"CorporateName"' + " : "  +   '"' + Globals.corporateName + '"' + " , ";
		jsonTxt += '"MenuItemAssignments"' + " : " + " [ ";


		for (let i = 0; i < myObject.length; i++) {


			const item = Globals.ItemList.find((item) => item.posNumber.toString() === myObject[i].PosNumber);

			if (item) {

				if (myObject[i].AlaCartePrice.toString().length > 0) {  //Ignore empty price items
					jsonTxt += " { ";
					jsonTxt += '"MenuItem"' + " : " + " { ";
					jsonTxt += '"MenuItemUniqueID"' + " : " + '"' + item.menuItemUniqueID + '"' + " , ";
					jsonTxt += '"MenuItemName"' + " : " + '"' + item.name + '"' + " , ";
					jsonTxt += '"MenuItemPOSNumber"' + " : " + '"' + item.posNumber + '"' + " , ";
					jsonTxt += '"OwnerUniqueID"' + " : " + '"' + Globals.storeUniqueID.toString() + '"' //+ " , ";
					//jsonTxt += '"OwnerUniqueID"' + " : " + '"' +  Globals.ownerUniqueID.toString() + '"' + " , ";
					//jsonTxt += '"CorporatePOSNumber"' + " : "  +   '"' + Globals.corporatePOSNumber + '"' + " , " ;
					//jsonTxt += '"CorporateName"' + " : "  +   '"' + Globals.corporateName + '"' ;
					jsonTxt += "},";
					jsonTxt += '"Price"' + " : " + '"' + myObject[i].AlaCartePrice + '"';
					jsonTxt += "}";
				} else {
					console.log("Item price is empty " + myObject[i].ItemName);
				}
				if (i < myObject.length - 1) {
					jsonTxt += ","
				}

			} else {
				console.log("Item not found " + myObject[i].ItemName);
				blnErrFound = true;
				Globals.TemplateErr = Globals.TemplateErr + '\n' + "Item not found " + myObject[i].ItemName;
				Globals.IgnoreRecords = Globals.IgnoreRecords + 1;
			}


		}
		jsonTxt += " ] ";

		if (this.state.isPriceLevel) {
			jsonTxt += "," + Globals.PriceLevelChange;
		}
		jsonTxt += " }]} ";

		Globals.JSONPriceChangeReq = jsonTxt;
		console.log((jsonTxt));

		if (blnErrFound) {
			this.toggleErrDiv();
			this.GenerateErrReport();
		} else {
			url = '/update';
		}

		this.toggleDiv();
		this.toggleValidationComplete();


	}

	resetGlobalCounts() {
		Globals.IgnoreRecords = 0;
		Globals.TypeOfChangeLog = "";
		Globals.corporatePOSNumber = "";
		Globals.corporateName = "";
		Globals.corporateUniqueID = "";

	}

	readFile() {

		this.resetGlobalCounts();
		this.toggleDiv();

		var f = this.state.file;
		var name = f.name;
		const reader = new FileReader();
		reader.onload = (evt) => {
			// evt = on_file_select event
			/* Parse data */
			const bstr = evt.target.result;
			const wb = XLSX.read(bstr, { type: "binary" });

			/* Get worksheets */
			for (let i = 0; i < wb.SheetNames.length; i++) {
				const wsname = wb.SheetNames[i];
				this.getWorkSheet(wsname, wb, i)
			}
		};
		reader.readAsBinaryString(f);

		this.getMenuItems();
	}

	getWorkSheet(wsname, wb, indx) {

		const ws = wb.Sheets[wsname];
		const data = XLSX.utils.sheet_to_csv(ws, { header: 1 });
		console.log("Sheet:" + wsname + ":Data>>>" + data);
		console.log(this.convertToJson(data, indx));

	}

	convertToJson(csv, indx) {
		var lines = csv.split("\n");
		var result = [];
		var headers = lines[0].split(",");
		Globals.TotalRecords = lines.length - 1;

		for (var i = 1; i < lines.length; i++) {
			var obj = {};
			var currentline = lines[i].split(",");

			for (var j = 0; j < headers.length; j++) {
				obj[headers[j]] = currentline[j];

			}
			result.push(obj);
		}

		switch (indx) {

			case 0:
				Globals.TemplateData = JSON.stringify(result);
				Globals.TypeOfChangeLog = "Performing Site level price updates for the effective date: " + Globals.ActivationDate
				break;
			case 1:
				Globals.TemplateDataPriceLevel = JSON.stringify(result);
				this.setState({ isPriceLevel: true });
				Globals.TypeOfChangeLog = "Performing Price level price updates for the effective date: " + Globals.ActivationDate
				break;
			default:
				console.info("error in spreadsheet");

		}

		return JSON.stringify(result); //JSON
	}

	render() {


		const { isloadingDivVisible } = this.state;
		const { isErrDivVisible } = this.state;
		const { isValidationComplete } = this.state;



		const onIdle = () => {
			console.log("user is idle");
		}

		return (

			<div>
				<h1>
					<Badge color="secondary">Upload Price Change Template</Badge>
				</h1>

				<Row>
					<Col md="8" lg="50">
						<Card body>
							<div>
								<Button color="secondary" className="ms-3" outline disabled>
									CFC Environment: <Badge color="primary">{Globals.SelectedCustomer}&nbsp;/&nbsp;{Globals.SelectedEnvironment}</Badge>
								</Button>
								<Button color="secondary" className="ms-3" outline disabled>
									Activation Date: <Badge color="primary">{Globals.ActivationDate}</Badge>
								</Button>
							</div>
						</Card>
					</Col>
				</Row>

				<IdleTimerProvider
					timeout={10000 * 60}
					onIdle={onIdle}
				/>
				<Row>

					<Col md="8" lg="40">
						Step 1
						<Card body>

							<CardText>
								<input
									type="file"
									id="file"
									accept=".xlsx"
									ref="fileUploader"
									onChange={this.filePathset.bind(this)}
								/>


							</CardText>

						</Card>
					</Col>

					<Col md="8" lg="40">
						Step 2
						<Card body>

							<CardText>
								<button
									onClick={() => { this.resetAlertStatus(); this.readFile(); }}
									disabled={this.state.file === "" || this.state.file === null || this.state.file === undefined} color="secondary">
									Validate Template
								</button>
							</CardText>

						</Card>
					</Col>
				</Row>

				<CardBody className="">

					<ButtonGroup>
						<Link style={{ pointerEvents: !isValidationComplete ? 'none' : '' }} to='/selector'>
							<Button disabled={!isValidationComplete} color="secondary"><b>Continue</b></Button>
						</Link>
					</ButtonGroup>

				</CardBody>
				<div id="loadingDiv" style={{ display: (isloadingDivVisible) ? 'block' : 'none' }}>
					<br />
					<br />
					<Alert color="danger"> Please wait while we validate the file</Alert>
					<br />
					<img src={LoadingAnim} style={{ "position": "relative", top: -220 }} />
				</div>

				<br />
				<br />
				<br />
				<br />
				<Alert style={{ display: (isErrDivVisible) ? 'block' : 'none' }} color="danger"> {this.state.alertArry.map((item) => <li key={item}>{item}</li>)}<br /><br /> Please view the error report that is downloaded for details.</Alert>
				<Alert style={{ display: (isValidationComplete & !isErrDivVisible) ? 'block' : 'none' }}>Validation Successful</Alert>

			</div>
		);
	}
}

export default ExcelToJson;
import React from "react";
import { Link } from "react-router-dom";
import {
  Navbar,
  Collapse,
  Nav,
  NavItem,
  NavbarBrand,
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  Dropdown,
  Button,
} from "reactstrap";
import Logo from "./Logo";
import NCRLogo from "../assets/images/logos/ncr.svg";
import { ReactComponent as LogoWhite } from "../assets/images/logos/ncr.svg";
import user1 from "../assets/images/users/user4.jpg";
import { useNavigate } from "react-router-dom";
import Gear from "../assets/images/logos/Gear.png"
import ProdGear from "../assets/images/logos/ProdGear.png"
import Globals from '../Globals';
import { version } from "../../package.json"

//Set application version number globally
Globals.ApplicationVersion = version

const Header = () => {
  const [isOpen, setIsOpen] = React.useState(false);

  const [dropdownOpen, setDropdownOpen] = React.useState(false);

  const [isShown, setDropdownIsVisable] = React.useState(false);

  const toggle = () => setDropdownOpen((prevState) => !prevState);
  const Handletoggle = () => {
    setIsOpen(!isOpen);
  };
  const showMobilemenu = () => {
    document.getElementById("sidebarArea").classList.toggle("showSidebar");
  };
  const logo = require('../assets/images/logos/ncr.png'); // with require

  const nav = useNavigate();
  const doNav = () => {
    nav("/login");
    nav(0);
  }
  return (

    <Navbar color="white" light expand="md" className="fix-header">

      <img src={NCRLogo} style={{ height: 80, width: 80 }} />

      <header style={{ textAlign: 'center', width: '100%', fontSize: 35 }} >
        <b>CFC Mass Price Update Tool</b>
        <center style={{ fontSize: 14 }}>Version: {version}</center>
      </header>

      <div className="d-flex align-items-center">
        <div className="d-lg-block d-none me-5 pe-3">
        </div>

        <NavbarBrand href="/">
          <LogoWhite className="d-lg-none" />
        </NavbarBrand>

        <Button
          color="primary"
          className=" d-lg-none"
          onClick={() => showMobilemenu()}
        >
          <i className="bi bi-list"></i>
        </Button>
      </div>
      <div className="hstack gap-2">
        <Button
          color="primary"
          size="sm"
          className="d-sm-block d-md-none"
          onClick={Handletoggle}
        >
          {isOpen ? (
            <i className="bi bi-x"></i>
          ) : (
            <i className="bi bi-three-dots-vertical"></i>
          )}
        </Button>
      </div>

      <div><img src={process.env.NODE_ENV == 'development' ? Gear : ProdGear} style={{ height: 20, width: 20 }} />
      </div>

      <div>
      &nbsp;&nbsp;{Globals.Username}
      </div>

      <Collapse navbar isOpen={isOpen}>
        <Nav className="me-auto" navbar>
        </Nav>

        <Dropdown id={'LogOut'} isOpen={dropdownOpen} toggle={toggle} style={{ display: isShown ? 'block' : 'none' }}>
          <DropdownToggle color="transparent">
            <img
              src={user1}
              alt="profile"
              className="rounded-circle"
              width="30"
            ></img>
          </DropdownToggle>

          <DropdownMenu>
            <DropdownItem onClick={doNav}>Logout</DropdownItem>
          </DropdownMenu>

        </Dropdown>
      </Collapse>
    </Navbar>
  );
};

export default Header;

using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace AMPSMiddleWare
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
			
			 services.AddCors(options =>
			{
				options.AddPolicy("AllowReactApp", builder =>
				{
					builder.WithOrigins("http://**************") // Your React app's origin
						   .AllowAnyMethod() // Allow GET, POST, etc.
						   .AllowAnyHeader() // Allow custom headers
						   .AllowCredentials(); // Allow cookies or authentication headers
				});
			});
			
            services.AddControllers().AddJsonOptions(options => options.JsonSerializerOptions.IgnoreNullValues = true); ;

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            // Setup logger
            loggerFactory.AddLog4Net();

            // global cors policy
			/*
            app.UseCors(x => x
                .AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader());
			*/
			
			app.UseCors(policy =>
				policy.WithOrigins("http://**************")
				  .AllowAnyMethod()
				  .AllowAnyHeader()
				  .AllowCredentials());

			
			app.UseCors("AllowReactApp");	
            
			app.UseRouting();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}

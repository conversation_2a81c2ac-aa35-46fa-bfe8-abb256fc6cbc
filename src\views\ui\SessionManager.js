import Globals from '../../Globals'; 

let sessionTimeout;

export function startSessionTimeout(logoutCallback, initialTimeout) {
  clearTimeout(sessionTimeout);
  sessionTimeout = setTimeout(logoutCallback, initialTimeout);
}

export function resetSessionTimeout(logoutCallback, initialTimeout) {
  clearTimeout(sessionTimeout);
  startSessionTimeout(logoutCallback, initialTimeout);
}

export function clearSessionTimeout() {
  clearTimeout(sessionTimeout);
}


export function sessionLogout() {
  // Perform logout actions (e.g., clear user data, redirect to login)
  // Clear the session timeout
  clearSessionTimeout();
  // Redirect to the login page or display a session expired message
}  

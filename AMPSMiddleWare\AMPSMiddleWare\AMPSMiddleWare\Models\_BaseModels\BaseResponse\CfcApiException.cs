﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models._BaseModels.BaseResponse
{
    public class CfcApiException : Exception
    {
        public CfcApiException(ResponseHeader header)
        {
            this.Statuses = header.Statuses;
            this.Success = header.Success;
        }

        public List<ResponseMessage> Statuses { get; set; }
        public bool Success { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChangePromoAssignmentUnion
    {
        public PriceChangePromotionBogo Bogo { get; set; }
        public PriceChangePromotionCheckReduction CheckReduction { get; set; }
        public PriceChangePromotionCombo Combo { get; set; }
        public PriceChangePromotionCoupon Coupon { get; set; }
        public PriceChangePromotionGroupCombo GroupCombo { get; set; }
        public PriceChangePromotionNewPrice NewPrice { get; set; }
        public PriceChangePromotionPackage Package { get; set; }
        public PriceChangePromotionQuickCombo QuickCombo { get; set; }
    }
}

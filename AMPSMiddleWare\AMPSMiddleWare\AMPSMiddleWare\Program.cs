using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace AMPSMiddleWare
{
    public class Program
    {
        public static void Main(string[] args)
        {            
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                   
                    
                    log4net.GlobalContext.Properties["Category"] = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version;
                    log4net.GlobalContext.Properties["Session"] = "";
                    log4net.GlobalContext.Properties["ClientVersion"] = "";


                });
    }
}

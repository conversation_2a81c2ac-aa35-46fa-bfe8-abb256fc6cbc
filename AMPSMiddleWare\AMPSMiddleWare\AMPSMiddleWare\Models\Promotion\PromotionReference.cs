﻿using AMPSMiddleWare.Models.Owner;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.Promotion
{
    public class PromotionReference : OwnerReference
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Guid PromotionUniqueID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PromotionID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PromotionName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int PromotionPOSNumber { get; set; }
    }
}

import React, { useState,useEffect } from "react";
import Globals from '../../Globals';
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { IdleTimerProvider, useIdleTimerContext } from 'react-idle-timer/dist/index.legacy.esm.js'
import {
  Alert,
  UncontrolledAlert,
  Card,
  CardBody,
  CardTitle,Badge,Button,ButtonGroup
} from "reactstrap";

const Main = styled("div")`
  font-family: sans-serif;
   
  height: 100vh;
`;

const History = () => {
	
	useEffect(() => {
		getHistoryData();
		if(!Globals.isUserLoggedIn){
			navigate('/login');	
		}
	},[]);
	var dynamicDivs = "";	
	 const [isloadingDivVisible, setloadingDivVisibility] = useState(false)
	 const [isRecentHistory1Visible, setRH1Visibility] = useState(false)
  const [isRecentHistory2Visible, setRH2Visibility] = useState(false)
  const [isRecentHistory3Visible, setRH3Visibility] = useState(false)
  const [isRecentHistory4Visible, setRH4Visibility] = useState(false)
  const [isRecentHistory5Visible, setRH5Visibility] = useState(false)
 
  const navigate = useNavigate();  
  const idleTimer = useIdleTimerContext() 
  
   const onIdle = () => {
     console.log("user is idle");
	 navigate('/login');	
  }	
	
 
 
const getHistoryData=async()=> {
 	
  let response = await fetch(Globals.BaseURL+'/api/History', { 
	    method: 'Get',
		 
          
		  headers: {
            'Content-type': 'application/json; charset=UTF-8',
			'Accept' : 'application/json',
			'Accept-Encoding' : 'gzip,deflate',
			 
         },
	   });
	   
	   let data = await response.json();	
	    
	   const historyData = data.split('\n').reverse();
	  
	   
	   
	   
	   for (var i = 0 ; i < historyData.length ; i++){
			if(historyData[0].length > 0){
				if( i ==0 ){
					setRH1Visibility(true);
					Globals.RecentHistory1 = historyData[0];
				}
				if( i == 1 ){
					setRH2Visibility(true);
					Globals.RecentHistory2 = historyData[1];
				}
				if( i == 2 ){
					if(historyData[2].length > 0)
					setRH3Visibility(true);
					Globals.RecentHistory3 = historyData[2];
				}
				if( i == 3 ){
					if(historyData[3].length > 0)
					setRH4Visibility(true);
					Globals.RecentHistory4 = historyData[3];
				}
				if( i == 4 ){
					setRH5Visibility(true);
					Globals.RecentHistory5 = historyData[4];
				} 
			}else{
				if( i ==0 ){
					setRH1Visibility(true);
					Globals.RecentHistory1 = historyData[1];
				}
				if( i == 1 ){
					setRH2Visibility(true);
					Globals.RecentHistory2 = historyData[2];
				}
				if( i == 2 ){
					if(historyData[2].length > 0){
						setRH3Visibility(true);
						Globals.RecentHistory3 = historyData[3];
					}
				}
				if( i == 3 ){
					if(historyData[2].length > 0){	
					setRH4Visibility(true);
					Globals.RecentHistory4 = historyData[4];
					}
				}
				if( i == 4 ){
					setRH5Visibility(true);
					Globals.RecentHistory5 = historyData[5];
				} 
				
			}
	   }
	   		 
	   //console.log(dynamicDivs);
	 
		
	   
}

	 
	   
  
	
  return (
  
 
  
   
    <div>
       <h1>
			<Badge color="secondary">History</Badge>
		</h1>	
		
		<IdleTimerProvider
      timeout={1000 * 10}      
      onIdle={onIdle} 
     
      /> 
		
		
		 <Card>
        <CardTitle tag="h6" className="border-bottom p-3 mb-0">          
          <b>Recent top 5 updates</b>
        </CardTitle>
		
        <CardBody className="">
          <div className="mt-3">
           	   
			    <Alert color="success"   style={{display: (isRecentHistory1Visible) ? 'block' : 'none'}}>
			 <b>{Globals.RecentHistory1}</b>   
			 
            </Alert>
			
			<Alert color="success"  style={{display: (isRecentHistory2Visible) ? 'block' : 'none'}}> 
			 <b>{Globals.RecentHistory2}</b>  		  
            </Alert>
			 
		     <Alert color="success" style={{display: (isRecentHistory3Visible) ? 'block' : 'none'}}> 
			 <b>{Globals.RecentHistory3}</b>   
			 
            </Alert>
			
			<Alert color="success" style={{display: (isRecentHistory4Visible) ? 'block' : 'none'}}> 
			 <b>{Globals.RecentHistory4}</b>  		  
            </Alert>
			  
			 
			<Alert color="success" style={{display: (isRecentHistory5Visible) ? 'block' : 'none'}}> 
			 <b>{Globals.RecentHistory5}</b>  		  
            </Alert>
					 
             
          </div>
        </CardBody>
      </Card>
		
    </div>
	 
  );
 
};

export default History; 

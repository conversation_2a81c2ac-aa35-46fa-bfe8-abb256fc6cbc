﻿using log4net;
using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;

namespace AMPSMiddleWare.Log
{
    public partial class Logger : ILogger
    {

        public Logger()
        {
            log4net.Config.XmlConfigurator.Configure();
            this.Log = log4net.LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        }

        public Logger(string name)
        {
            log4net.Config.XmlConfigurator.Configure();
            this.Log = log4net.LogManager.GetLogger(name);
        }

        public Logger(string name, FileInfo configFile)
        {
            log4net.Config.XmlConfigurator.Configure(configFile);
            this.Log = log4net.LogManager.GetLogger(name);
        }


        #region Properties 

        private readonly ILog Log;

        #endregion Properties

        #region Methods

        #region Debug

        [DebuggerStepThrough()]
        public void LogDebug(string message)
        {
            this.Log.Debug(message);
        }

        [DebuggerStepThrough()]
        public void LogDebug(string message, string category)
        {
            this.SetCategory(category);
            this.Log.Debug(message);
        }

        [DebuggerStepThrough()]
        public void LogDebug(string message, Exception ex)
        {
            this.Log.Debug(message, ex);
        }

        [DebuggerStepThrough()]
        public void LogDebug(string message, string category, Exception ex)
        {
            this.SetCategory(category);
            this.Log.Debug(message, ex);
        }

        #endregion Debug

        #region Information

        [DebuggerStepThrough()]
        public void LogInformation(string message)
        {
            this.Log.Info(message);
        }

        [DebuggerStepThrough()]
        public void LogInformation(string message, string category)
        {
            this.SetCategory(category);
            this.Log.Info(message);
        }

        #endregion Information

        #region Warning

        [DebuggerStepThrough()]
        public void LogWarning(string message)
        {
            this.Log.Warn(message);
        }

        [DebuggerStepThrough()]
        public void LogWarning(string message, string category)
        {
            this.SetCategory(category);
            this.Log.Warn(message);
        }

        [DebuggerStepThrough()]
        public void LogWarning(string message, string category, Exception ex)
        {
            this.SetCategory(category);
            this.Log.Warn(message, ex);
        }

        [DebuggerStepThrough()]
        public void LogWarning(string message, Exception ex)
        {
            this.Log.Warn(message, ex);
        }
        #endregion Warning

        #region Exception

        [DebuggerStepThrough()]
        public void LogException(string message, Exception ex)
        {
            this.Log.Error(message, ex);
        }

        [DebuggerStepThrough()]
        public void LogException(string message, string category, Exception ex)
        {
            this.SetCategory(category);
            this.Log.Error(message, ex);
        }

        #endregion Exception

        #region Fatal

        [DebuggerStepThrough()]
        public void LogFatalException(string message, Exception ex)
        {
            this.Log.Fatal(message, ex);
        }

        [DebuggerStepThrough()]
        public void LogFatalException(string message, string category, Exception ex)
        {
            this.SetCategory(category);
            this.Log.Fatal(message, ex);
        }

        #endregion Fatal

        #region Helpers

        private void SetCategory(string category)
        {
            log4net.LogicalThreadContext.Properties["Category"] = category;
        }

        #endregion Helpers

        #endregion Methods
    }



}

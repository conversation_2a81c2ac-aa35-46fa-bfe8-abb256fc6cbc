﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;

using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Cors;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Json.Net;
using System.IO;
using Newtonsoft.Json.Linq;

 
using AMPSMiddleWare.Settings;
using AMPSMiddleWare.Models.Store;
using AMPSMiddleWare.Models.Owner;
using AMPSMiddleWare.Models.PriceChange;
using AMPSMiddleWare.Models.MenuItem;
using AMPSMiddleWare.Models.Promotion;

using AMPSMiddleWare.Models._BaseModels.BaseRequest;
using AMPSMiddleWare.Models._BaseModels.BaseResponse;
using AMPSMiddleWare.Helpers;
using Microsoft.AspNetCore.Http;
using AMPSMiddleWare.Models.TableDefinitions;
using AMPSMiddleWare.Models.ActivationSchedule;
using AMPSMiddleWare.Models.PriceLevel;
using AMPSMiddleWare.Models.Export;
using Microsoft.Extensions.Configuration;

namespace AMPSMiddleWare.Controllers
{
    [ApiController]
    [Route("")]
    [EnableCors("AllowAll")]
    public class CFCPriceChangeController : ControllerBase
    {
         
        private readonly ILogger<CFCPriceChangeController> _logger;
        private CFCSettings _settings;
        private readonly JsonSerializerOptions _jsonSerializerCustomOption;
        private readonly AuthenticationHeaderValue _authenticationHeader;
        private HttpClient _httpClient;
        private readonly string baseUrl;

        private readonly string username; // = "APIUser";
        private readonly string password; // = "amps@CFC09";


        public CFCPriceChangeController(ILogger<CFCPriceChangeController> logger)
        {
            _logger = logger;
            _settings = new CFCSettings();

            IConfigurationRoot _config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetParent(AppContext.BaseDirectory).FullName)
                .AddJsonFile("appsettings.json", false)
                .Build();

            username = _config.GetSection("CFCSettings").GetValue<string>("UserName");
            string encryptedPassword = _config.GetSection("CFCSettings").GetValue<string>("Password");
            password = EncryptionHelper.DecryptAES(string.IsNullOrEmpty(encryptedPassword)?"": encryptedPassword);
            baseUrl = _config.GetSection("CFCSettings").GetValue<string>("BaseUrl");

            _settings.ClientApplication = _config.GetSection("CFCSettings").GetValue<string>("ClientApplication");
            _settings.ClientVersion = _config.GetSection("CFCSettings").GetValue<string>("ClientVersion");
            _settings.ApiVersion = _config.GetSection("CFCSettings").GetValue<string>("ApiVersion");
            _settings.MessageID = _config.GetSection("CFCSettings").GetValue<string>("MessageID");

            // baseUrl = "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-test";
            // baseUrl = "https://web.configurationcenter.com/ConfigurationCenter/8967";

            var handler = new SocketsHttpHandler() { PooledConnectionLifetime = TimeSpan.FromMinutes(5) };
            _httpClient = new HttpClient(handler)
            {
                Timeout = new TimeSpan(0, 10, 0)
            };

            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            _httpClient.DefaultRequestHeaders.Authorization = _authenticationHeader;

            _authenticationHeader = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes($"{username}:{password}")));

        }

        public class Items
        {
            public string ItemID { get; set; }
            public string Price { get; set; }
            public string UniqueID  { get; set; }
        }


        /// <summary>Send request tp CFC APi</summary>
        /// <returns>OK</returns>
        [HttpPost, Route("PriceChanges1")]

        public string PriceChanges1([FromBody] dynamic requestRaw)
        {

            string jsonString = System.Text.Json.JsonSerializer.Serialize(requestRaw);  
            var items = JsonConvert.DeserializeObject<List<Items>>(jsonString);

            foreach (var item in items)
            {
                var name = item.ItemID;
               
            } 
            

            return jsonString;


        }


            /// <summary>Send request tp CFC APi</summary>
            /// <returns>OK</returns>
            [HttpPost, Route("PriceChanges")]

        public async Task<string> PriceChanges([FromBody] dynamic requestRaw)
        {
            string sFunc = "PriceChanges[POST] - ";
            var context = HttpContext;
           
             
            //var urlBuilder = new StringBuilder("https://web.configurationcenter.com/ConfigurationCenter/amps-lab-test/ws/MenuItems/UpdateMenuItems");
            var urlBuilder = new StringBuilder(baseUrl +  "/ws/MenuItems/UpdateMenuItems");
           
            var responseStream = "";
            HttpClient client = new HttpClient();
             

            string jsonString = System.Text.Json.JsonSerializer.Serialize(requestRaw);
            var items = JsonConvert.DeserializeObject<List<Items>>(jsonString);

            if (requestRaw is null)
            {
                _logger.LogInformation(sFunc + $"Bad Request - Request is NULL");                 
                await context.Response.WriteAsync("ERROR MSG:" + "Bad Request - Request is NULL");
                return BadRequest().ToString();
            }

            client.BaseAddress = new Uri(urlBuilder.ToString());
            var request = new HttpRequestMessage();
            request.Method = HttpMethod.Post;

            
            StringBuilder sb = new StringBuilder();
            StringWriter sw = new StringWriter(sb);

            using (JsonWriter writer = new JsonTextWriter(sw))
            {
                writer.Formatting = Formatting.Indented;

                writer.WriteStartObject();

                writer.WritePropertyName("Header");
                

                writer.WriteStartObject();
                writer.WritePropertyName("ClientApplication");
                writer.WriteValue(_settings.ClientApplication);
                writer.WritePropertyName("ClientVersion");
                writer.WriteValue(_settings.ClientVersion);
                writer.WritePropertyName("ApiVersion");
                writer.WriteValue(_settings.ApiVersion);
                writer.WritePropertyName("MessageID");
                writer.WriteValue(_settings.MessageID);
                writer.WriteEndObject();
                writer.WritePropertyName("Batch");
                writer.WriteStartArray();
                
                

                foreach (var item in items)
                {
                    writer.WriteStartObject();
                    writer.WritePropertyName("MenuItemUniqueID");
                    writer.WriteValue(item.UniqueID);

                    writer.WritePropertyName("Price");
                    writer.WriteValue(item.Price);
                    writer.WriteEnd();
                } 
                
                writer.WriteEndObject();

            }
            
            //return sb.ToString();

             
             
             //request.Content = new StringContent(JsonConvert.SerializeObject(sb.ToString(), Formatting.Indented), Encoding.UTF8, "application/json");
            request.Content = new StringContent(sb.ToString(), Encoding.UTF8, "application/json");
            //JsonConvert.SerializeObject(obj), Encoding.UTF8, "application/json"

            string svcCredentials = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(username + ":" + password));

           
           // var json = "{\"Header\": \}""   "\", \"balance\": 50 }";


            //client.DefaultRequestHeaders.Accept.Clear();

            request.Headers.Add("Accept", "application/json");
            request.Headers.Add("Connection", "keep-alive");
            request.Headers.Add("Accept-Encoding", "gzip,deflate");
            request.Headers.Add("Authorization", "Basic " + svcCredentials);
            
            var response = await client.SendAsync(request);
            //var response = await client.PostAsync(client.BaseAddress, request.Content);
             
            //response.EnsureSuccessStatusCode();

            //responseStream = await response.Content.ReadAsStringAsync();
            return response.ToString();
            //return responseStream;
            
        }

        [HttpPost, Route("UpdatePriceChanges")]
        public async Task<dynamic> UpdatePriceChanges([FromBody] dynamic requestRaw)
        {

            string sFunc = "PriceChanges[POST] - ";
            var context = HttpContext;


           // var urlBuilder = new StringBuilder("https://web.configurationcenter.com/ConfigurationCenter/8967/ws/PriceChanges/CreatePriceChanges");
            var urlBuilder = new StringBuilder(baseUrl).Append("/ws/PriceChanges/CreatePriceChanges").ToString();

            var responseStream = "";
            HttpClient client = new HttpClient();

            if (requestRaw is null)
            {
                _logger.LogInformation(sFunc + $"Bad Request - Request is NULL");
                await context.Response.WriteAsync("ERROR MSG:" + "Bad Request - Request is NULL");
                return BadRequest().ToString();
            }


            string jsonString = System.Text.Json.JsonSerializer.Serialize(requestRaw);

            client.BaseAddress = new Uri(urlBuilder.ToString());
            var request = new HttpRequestMessage();
            request.Method = HttpMethod.Post;

            request.Content = new StringContent(jsonString.ToString(), Encoding.UTF8, "application/json");
            //JsonConvert.SerializeObject(obj), Encoding.UTF8, "application/json"

            string svcCredentials = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(username + ":" + password));
             

            request.Headers.Add("Accept", "application/json");
            request.Headers.Add("Connection", "keep-alive");
            request.Headers.Add("Accept-Encoding", "gzip,deflate");
            request.Headers.Add("Authorization", "Basic " + svcCredentials);

            var response = await client.SendAsync(request);

            var restInTxt = response.ToString();

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

//            using var responseContentStream = await response.Content.ReadAsStreamAsync();
//            return await System.Text.Json.JsonSerializer.DeserializeAsync<dynamic>(responseContentStream, _jsonSerializerCustomOption);

            return response.ToString();

        }

        [HttpPost, Route("UpdateCurrentPriceChanges")]
        public async Task<dynamic> UpdateCurrentPriceChanges([FromBody] dynamic requestRaw)
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceChanges/UpdatePriceChanges").ToString();

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(requestRaw));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
             return await System.Text.Json.JsonSerializer.DeserializeAsync<dynamic>(responseContentStream, _jsonSerializerCustomOption);
        }


        [HttpPost, Route("OwnersList")]
        public async Task<ListOwnersRes> ListOwnersAsync()
        {
             
            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/Owners").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<ListOwnersRes>(responseContentStream, _jsonSerializerCustomOption);
        }



        [HttpPost, Route("StoreList")]
        public async Task<ListStoresRes> ListStoresAsync()
        {
             
            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/Stores").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");           
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization",_authenticationHeader.ToString());            
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<ListStoresRes>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpPost, Route("GetStores")]
        public async Task<dynamic> GetStoresAsync([FromBody] dynamic requestRaw)
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/Stores/GetStores").ToString();

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(requestRaw));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<dynamic>(responseContentStream, _jsonSerializerCustomOption);
        }

        [HttpPost, Route("GetPriceLevels")]
        public async Task<PriceLevelRes> GetPriceLevelsAsync()
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceLevels").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<PriceLevelRes>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpPost, Route("getPromotions")]
        public async Task<ListPromotionsRes> ListPromotionsAsync()
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/Promotions").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<ListPromotionsRes>(responseContentStream, _jsonSerializerCustomOption);


        }



        [HttpPost, Route("ItemList")]
        public async Task<ListMenuItemsRes> ItemListAsync()
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/MenuItems").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<ListMenuItemsRes>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpPost, Route("GetPriceChanges")]
        public async Task<ListPriceChangesRes> GetPriceChanges()
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceChanges").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<ListPriceChangesRes>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpPost, Route("CheckGlobalPricing")]
        public async Task<string> CheckGlobalPricing()
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceChanges").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();


            dynamic jsonObject = await (System.Text.Json.JsonSerializer.DeserializeAsync<ListPriceChangesRes>(responseContentStream, _jsonSerializerCustomOption));


            StringBuilder sb = new StringBuilder();
            StringWriter sw = new StringWriter(sb);

            using (JsonWriter writer = new JsonTextWriter(sw))
            {
                writer.Formatting = Formatting.Indented;

                //writer.WriteStartObject();

                writer.WritePropertyName("Header");


                writer.WriteStartObject();
                writer.WritePropertyName("ClientApplication");
                writer.WriteValue(_settings.ClientApplication);
                writer.WritePropertyName("ClientVersion");
                writer.WriteValue(_settings.ClientVersion);
                writer.WritePropertyName("ApiVersion");
                writer.WriteValue(_settings.ApiVersion);
                writer.WritePropertyName("MessageID");
                writer.WriteValue(_settings.MessageID);
                 
                writer.WriteEndObject();
                writer.WriteRaw(",");

                //writer.WriteEndConstructor();
                //writer.WriteEnd();
                writer.WritePropertyName("References");
                writer.WriteStartArray();



                for (int i = 0; i < jsonObject.List.Count; i++)
                {
                    writer.WriteStartObject();
                    writer.WritePropertyName("PriceChangeUniqueID");
                    writer.WriteValue(jsonObject.List[i].PriceChangeUniqueID); 
                    writer.WriteEnd();
                }

                //writer.WriteEndObject();

            }

            var urlBuilder = new StringBuilder("https://web.configurationcenter.com/ConfigurationCenter/amps-lab-test/ws/PriceChanges/GetPriceChanges");
            var responseStream = "";
            HttpClient client = new HttpClient();

            client.BaseAddress = new Uri(urlBuilder.ToString());
            var request = new HttpRequestMessage();
            request.Method = HttpMethod.Post;

              
             
            request.Content = new StringContent(sb.ToString(), Encoding.UTF8, "application/json");
             
            string svcCredentials = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(username + ":" + password));
             
            request.Headers.Add("Accept", "application/json");
            request.Headers.Add("Connection", "keep-alive");
            request.Headers.Add("Accept-Encoding", "gzip,deflate");
            request.Headers.Add("Authorization", "Basic " + svcCredentials);

            response = await client.SendAsync(request);
            
            return response.ToString();
            //return responseStream;


        }




        [HttpPost, Route("PriceChangeSchedule")]
        public async Task<PriceChange> PriceChangeSchedule()
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/Stores").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<PriceChange>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpPost, Route("TableDefinitions")]
        public async Task<ListTableDefinitionsRes> getTableDefinitions([FromBody] dynamic requestRaw)
        {
             
            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/TableDefinitions/CollectTableDefinitionsForStore").ToString();

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(requestRaw));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<ListTableDefinitionsRes>(responseContentStream, _jsonSerializerCustomOption);
 

        }

        [HttpPost, Route("ListActivationSchedules")]
        public async Task<GetActivationSchedulesRes> ListActivationSchedules()
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/ActivationSchedules").ToString();

            var content = new StringContent(JsonConvert.SerializeObject(new RequestBase(_settings)));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<GetActivationSchedulesRes>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpPost, Route("CollectPriceLevelsForStore")]
        public async Task<PriceLevelRes> getPriceLevelForStore([FromBody] dynamic requestRaw)
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceLevels/CollectPriceLevelsForStore").ToString();

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(requestRaw));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<PriceLevelRes>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpPost, Route("CollectStoreSchedule")]
        public async Task<ListPriceChangesRes> getStoreSchedule([FromBody] dynamic requestRaw)
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceChanges/CollectPriceChangesForStore").ToString();

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(requestRaw));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<ListPriceChangesRes>(responseContentStream, _jsonSerializerCustomOption);


        }


        [HttpPost, Route("CollectPriceChangesForStore")]
        public async Task<dynamic> getPriceChangesForStore([FromBody] dynamic requestRaw)
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceChanges/CollectPriceChangesForStore").ToString();

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(requestRaw));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<dynamic>(responseContentStream, _jsonSerializerCustomOption);


        }


        [HttpPost, Route("ValidatePriceChange")]
        public async Task<GetPriceChangeRes> validatePriceChange([FromBody] dynamic requestRaw)
        {

            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/PriceChanges/GetPriceChange").ToString();

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(requestRaw));
            content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());
            HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, content);

            if (response.StatusCode != HttpStatusCode.OK)
                ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());

            using var responseContentStream = await response.Content.ReadAsStreamAsync();
            return await System.Text.Json.JsonSerializer.DeserializeAsync<GetPriceChangeRes>(responseContentStream, _jsonSerializerCustomOption);


        }

        [HttpGet, Route("ExportItemDataBatch")]
        public async Task<dynamic> ExportItemDataBatch()
        {

            Task<ListMenuItemsRes> list = ItemListAsync();

            //Create a list with all items 
            List<int> originalList = Enumerable.Range(1, list.Result.List.Count).ToList();
              
            int startcount = 0;
            int endcount = 100;
            int chunksize = 100;
            int count = 0;
            int batchcount = 100;

            List<ExportItemDataReq.Reference> referenceList = new List<ExportItemDataReq.Reference>();
            List<StringContent> batchRequests = new List<StringContent>();
            List <dynamic> responses = new List<dynamic>();

            ExportItemDataReq eirq = new ExportItemDataReq(_settings);
             

            for (var i = startcount; i < endcount; i++)
            {

                
                ExportItemDataReq.Reference newRef = new ExportItemDataReq.Reference
                {                     
                    MenuItemUniqueID = list.Result.List[i].MenuItemUniqueID.ToString()
                };

                referenceList.Add(newRef);
                
                if (i == endcount - 1)
                {
                    startcount += chunksize;
                    endcount += chunksize;

                    if (endcount > list.Result.List.Count)
                    {
                        endcount = list.Result.List.Count;
                    }
                    count += 1;
                    eirq.References = referenceList;
                   

                    var content = new StringContent(System.Text.Json.JsonSerializer.Serialize<ExportItemDataReq>(eirq, _jsonSerializerCustomOption), Encoding.UTF8, "application/json");
                    content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
                    content.Headers.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());

                    batchRequests.Add(content);
                    referenceList = new List<ExportItemDataReq.Reference>(); 

                }
            }  


            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/MenuItems/GetMenuItems").ToString();
             
            List<dynamic> objectList = new List<dynamic>();
            List<ExportItemDataRes> itemReference = new List<ExportItemDataRes>();
            List<ExportMenuItemHeader> itemList = new List<ExportMenuItemHeader>();
             
            foreach (var batchRequest in batchRequests)
            {
                HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, batchRequest);
               
                // ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());
                //continue;
                if (response.StatusCode != HttpStatusCode.OK)
                {
                    List<ExportItemDataReq.Reference> referenceListSingle = new List<ExportItemDataReq.Reference>();
                    ExportItemDataReq eirqSingle = new ExportItemDataReq(_settings);
                    List<StringContent> singleRequests = new List<StringContent>();
                    var batchendcount = 0;
                    batchendcount = batchcount + 100;
                    if(batchendcount > endcount)
                    {
                        batchendcount = endcount;
                    }
                    for (var i = batchcount; i < batchendcount; i++)
                    {
                        ExportItemDataReq.Reference newRefSingle = new ExportItemDataReq.Reference
                        {
                            MenuItemUniqueID = list.Result.List[i].MenuItemUniqueID.ToString()
                        };

                        referenceListSingle.Add(newRefSingle);
                        eirqSingle.References = referenceListSingle;

                        var content = new StringContent(System.Text.Json.JsonSerializer.Serialize<ExportItemDataReq>(eirqSingle, _jsonSerializerCustomOption), Encoding.UTF8, "application/json");
                        content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
                        content.Headers.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());

                        singleRequests.Add(content);
                        referenceListSingle = new List<ExportItemDataReq.Reference>();

                    }

                    foreach (var singleRequest in singleRequests)
                    {
                        HttpResponseMessage responseSingle = await _httpClient.PostAsync(_settings.BaseUrlCfc, singleRequest);

                        if (responseSingle.StatusCode != HttpStatusCode.OK)                           
                            continue;

                        using var responseContentStream = await responseSingle.Content.ReadAsStreamAsync();

                        var resp = await System.Text.Json.JsonSerializer.DeserializeAsync<ExportItemDataRes>(responseContentStream, _jsonSerializerCustomOption);

                        foreach (var item in resp.List)
                        {
                            ExportMenuItemHeader newItem = new ExportMenuItemHeader
                            {
                                LongName = item.LongName,
                                POSNumber = item.POSNumber
                            };

                            bool containsItem = itemList.Any(item => item.POSNumber == newItem.POSNumber);
                            if (!containsItem)
                            {
                                itemList.Add(newItem);
                            }
                        }


                    }



                }
                else { 
                  

                using var responseContentStream = await response.Content.ReadAsStreamAsync();
                 
                var resp = await System.Text.Json.JsonSerializer.DeserializeAsync<ExportItemDataRes>(responseContentStream, _jsonSerializerCustomOption);
                 
                    foreach (var item in resp.List)
                    {
                        ExportMenuItemHeader newItem = new ExportMenuItemHeader
                        {
                            LongName = item.LongName,
                            POSNumber = item.POSNumber
                        };

                        bool containsItem = itemList.Any(item => item.POSNumber == newItem.POSNumber);
                        if (!containsItem)
                        {
                            itemList.Add(newItem);
                        }
                    }
                }
                batchcount += 100;
            }
            return itemList; 
        }



        [HttpGet, Route("ExportItemData")]
        public async Task<dynamic> ExportItemData()
        {

            Task<ListMenuItemsRes> list = ItemListAsync();

            //Create a list with all items 
            List<int> originalList = Enumerable.Range(1, list.Result.List.Count).ToList();              
            List<StringContent> batchRequests = new List<StringContent>();
            _settings.BaseUrlCfc = new StringBuilder(baseUrl).Append("/ws/MenuItems/GetMenuItems").ToString();
            List<ExportItemDataReq.Reference> referenceList = new List<ExportItemDataReq.Reference>();
            ExportItemDataReq eirq = new ExportItemDataReq(_settings);

            for (var i = 0; i < list.Result.List.Count; i++)
            {
                // var URL = new StringBuilder(baseUrl).Append("/ws/MenuItems/GetMenuItem").ToString();
                //.Append(list.Result.List[i].MenuItemUniqueID.ToString()).ToString();

                ExportItemDataReq.Reference newRef = new ExportItemDataReq.Reference
                {
                    MenuItemUniqueID = list.Result.List[i].MenuItemUniqueID.ToString()
                };

                referenceList.Add(newRef);

                eirq.References = referenceList;


                var content = new StringContent(System.Text.Json.JsonSerializer.Serialize<ExportItemDataReq>(eirq, _jsonSerializerCustomOption), Encoding.UTF8, "application/json");
                content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json");
                content.Headers.TryAddWithoutValidation("Authorization", _authenticationHeader.ToString());

                batchRequests.Add(content);
                referenceList = new List<ExportItemDataReq.Reference>();

                //batchRequests.Add(URL); 
            }

            List<dynamic> objectList = new List<dynamic>();
            List<ExportItemDataRes> itemReference = new List<ExportItemDataRes>();
            List<ExportMenuItemHeader> itemList = new List<ExportMenuItemHeader>();

            foreach (var batchRequest in batchRequests)
            {
                HttpResponseMessage response = await _httpClient.PostAsync(_settings.BaseUrlCfc, batchRequest);

                if (response.StatusCode != HttpStatusCode.OK)
                    //ExceptionHelper.ThrowCfcApiException(await response.Content.ReadAsStringAsync());
                    continue;

                using var responseContentStream = await response.Content.ReadAsStreamAsync();

                var resp = await System.Text.Json.JsonSerializer.DeserializeAsync<ExportItemDataRes>(responseContentStream, _jsonSerializerCustomOption);
               
                   foreach (var item in resp.List)
                   {
                       ExportMenuItemHeader newItem = new ExportMenuItemHeader
                       {
                           LongName = item.LongName,
                           POSNumber = item.POSNumber
                       };

                        bool containsItem = itemList.Any(item => item.POSNumber == newItem.POSNumber);
                        if (!containsItem)
                        {
                            itemList.Add(newItem);
                        }
                } 
                 

            }
            return itemList;
        }


        

    }
}

﻿using AMPSMiddleWare.Models.Owner;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.ItemCategory
{
    public class ItemCategoryReference : OwnerReference
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Guid ItemCategoryUniqueID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ItemCategoryID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CategoryName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int CategoryPOSNumber { get; set; }
    }
}

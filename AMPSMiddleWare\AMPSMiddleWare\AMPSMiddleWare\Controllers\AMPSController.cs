﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AMPSMiddleWare.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class AMPSController : ControllerBase
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "<PERSON>racing", "Chi<PERSON>", "<PERSON>", "Mild", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hot", "Sweltering", "Scorching"
        };

        private readonly ILogger<AMPSController> _logger;

        public AMPSController(ILogger<AMPSController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public string Get()
        {
            _logger.LogInformation(System.Reflection.Assembly.GetExecutingAssembly().GetName().Name);
            var rng = new Random();
            return "API is live";
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AMPSMiddleWare.Log
{
    public interface ILogger
    {
        void LogDebug(string message);
        void LogDebug(string message, string category);
        void LogDebug(string message, Exception ex);
        void LogDebug(string message, string category, Exception ex);
        void LogInformation(string message);
        void LogInformation(string message, string category);
        void LogWarning(string message);
        void LogWarning(string message, string category);
        void LogWarning(string message, Exception ex);
        void LogWarning(string message, string category, Exception ex);
        void LogException(string message, Exception ex);
        void LogException(string message, string category, Exception ex);
        void LogFatalException(string message, Exception ex);
        void LogFatalException(string message, string category, Exception ex);
    }
}

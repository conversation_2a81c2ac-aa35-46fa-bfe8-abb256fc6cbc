# Multi-stage build for ASP.NET Core Backend
# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:3.1 AS build

# Set working directory
WORKDIR /src

# Copy project files
COPY AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/*.csproj ./AMPSMiddleWare/
COPY AMPSMiddleWare/AMPSMiddleWare/*.sln ./

# Restore dependencies
RUN dotnet restore AMPSMiddleWare/AMPSMiddleWare.csproj

# Copy source code
COPY AMPSMiddleWare/AMPSMiddleWare/AMPSMiddleWare/ ./AMPSMiddleWare/

# Build the application
WORKDIR /src/AMPSMiddleWare
RUN dotnet build AMPSMiddleWare.csproj -c Release -o /app/build

# Publish the application
RUN dotnet publish AMPSMiddleWare.csproj -c Release -o /app/publish

# Stage 2: Runtime
FROM mcr.microsoft.com/dotnet/aspnet:3.1 AS runtime

# Set working directory
WORKDIR /app

# Create directories for logs and data
RUN mkdir -p logs data

# Copy published application
COPY --from=build /app/publish .

# Set environment variables
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# Expose port
EXPOSE 80

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# Set the entry point
ENTRYPOINT ["dotnet", "AMPSMiddleWare.dll"]
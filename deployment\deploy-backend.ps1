# Backend Deployment Script for MenuOps Pricing Tool
# Usage: .\deploy-backend.ps1 -Environment "production" -TargetPath "C:\inetpub\wwwroot\menuops-api"

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Development", "Production", "QA")]
    [string]$Environment,

    [Parameter(Mandatory=$true)]
    [string]$TargetPath,

    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild = $false,

    [Parameter(Mandatory=$false)]
    [switch]$SelfContained = $false,

    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "AMPSMiddleWare"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$BackendPath = Join-Path $ProjectRoot "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare"

Write-Host "=== MenuOps Pricing Tool - Backend Deployment ===" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Target Path: $TargetPath" -ForegroundColor Yellow
Write-Host "Backend Path: $BackendPath" -ForegroundColor Yellow
Write-Host "Self-Contained: $SelfContained" -ForegroundColor Yellow

try {
    # Check if .NET Core is installed
    Write-Host "Checking .NET Core installation..." -ForegroundColor Cyan
    $dotnetVersion = dotnet --version 2>$null
    if (-not $dotnetVersion) {
        throw ".NET Core is not installed or not in PATH"
    }
    Write-Host ".NET Core version: $dotnetVersion" -ForegroundColor Green

    # Change to backend project directory
    if (-not (Test-Path $BackendPath)) {
        throw "Backend project path not found: $BackendPath"
    }
    Set-Location $BackendPath

    # Check if project file exists
    $projectFile = "AMPSMiddleWare.csproj"
    if (-not (Test-Path $projectFile)) {
        throw "Project file not found: $projectFile"
    }

    # Stop the service if it's running
    if (Get-Service -Name $ServiceName -ErrorAction SilentlyContinue) {
        Write-Host "Stopping service: $ServiceName" -ForegroundColor Cyan
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 5
    }

    # Build and publish the application if not skipped
    if (-not $SkipBuild) {
        Write-Host "Restoring NuGet packages..." -ForegroundColor Cyan
        dotnet restore
        if ($LASTEXITCODE -ne 0) {
            throw "dotnet restore failed"
        }

        Write-Host "Building ASP.NET Core application..." -ForegroundColor Cyan
        dotnet build -c Release
        if ($LASTEXITCODE -ne 0) {
            throw "dotnet build failed"
        }

        Write-Host "Publishing application for $Environment..." -ForegroundColor Cyan

        $publishArgs = @(
            "publish"
            "-c", "Release"
            "-o", "publish"
            "--verbosity", "minimal"
        )

        if ($SelfContained) {
            $publishArgs += @("--self-contained", "true", "-r", "win-x64")
        } else {
            $publishArgs += @("--self-contained", "false")
        }

        & dotnet @publishArgs
        if ($LASTEXITCODE -ne 0) {
            throw "dotnet publish failed"
        }

        Write-Host "Publish completed successfully!" -ForegroundColor Green
    }

    # Check if publish directory exists
    $PublishPath = Join-Path $BackendPath "publish"
    if (-not (Test-Path $PublishPath)) {
        throw "Publish directory not found at: $PublishPath"
    }

    # Create target directory if it doesn't exist
    if (-not (Test-Path $TargetPath)) {
        Write-Host "Creating target directory: $TargetPath" -ForegroundColor Cyan
        New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
    }

    # Backup existing deployment if it exists
    $BackupPath = "$TargetPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    if (Test-Path $TargetPath) {
        $existingFiles = Get-ChildItem $TargetPath -ErrorAction SilentlyContinue
        if ($existingFiles) {
            Write-Host "Creating backup at: $BackupPath" -ForegroundColor Cyan
            Copy-Item -Path $TargetPath -Destination $BackupPath -Recurse -Force
        }
    }

    # Clear target directory (except for logs and data folders)
    Write-Host "Clearing target directory..." -ForegroundColor Cyan
    Get-ChildItem $TargetPath -Exclude "logs", "data", "*.config" | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue

    # Copy published files to target
    Write-Host "Copying published files to target..." -ForegroundColor Cyan
    Copy-Item -Path "$PublishPath\*" -Destination $TargetPath -Recurse -Force

    # Update configuration for environment
    Write-Host "Updating configuration for $Environment..." -ForegroundColor Cyan
    $appSettingsPath = Join-Path $TargetPath "appsettings.json"
    if (Test-Path $appSettingsPath) {
        # Here you would update environment-specific settings
        # For now, we'll just copy the appropriate appsettings file
        $envSettingsFile = "appsettings.$Environment.json"
        $envSettingsPath = Join-Path $BackendPath $envSettingsFile
        if (Test-Path $envSettingsPath) {
            Copy-Item -Path $envSettingsPath -Destination (Join-Path $TargetPath $envSettingsFile) -Force
        }
    }

    # Set appropriate permissions
    Write-Host "Setting directory permissions..." -ForegroundColor Cyan
    $acl = Get-Acl $TargetPath
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl -Path $TargetPath -AclObject $acl

    # Verify deployment
    $mainDll = Join-Path $TargetPath "AMPSMiddleWare.dll"
    if (-not (Test-Path $mainDll)) {
        throw "Deployment verification failed: AMPSMiddleWare.dll not found"
    }

    # Start the service if it exists
    if (Get-Service -Name $ServiceName -ErrorAction SilentlyContinue) {
        Write-Host "Starting service: $ServiceName" -ForegroundColor Cyan
        Start-Service -Name $ServiceName
        Start-Sleep -Seconds 10

        $serviceStatus = Get-Service -Name $ServiceName
        if ($serviceStatus.Status -eq "Running") {
            Write-Host "Service started successfully!" -ForegroundColor Green
        } else {
            Write-Warning "Service failed to start. Status: $($serviceStatus.Status)"
        }
    }

    Write-Host "=== Deployment Completed Successfully! ===" -ForegroundColor Green
    Write-Host "Files deployed to: $TargetPath" -ForegroundColor Yellow
    Write-Host "Backup created at: $BackupPath" -ForegroundColor Yellow

    # Display deployment summary
    $deployedFiles = Get-ChildItem $TargetPath -Recurse -File
    Write-Host "Total files deployed: $($deployedFiles.Count)" -ForegroundColor Green

} catch {
    Write-Host "=== Deployment Failed! ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red

    # Try to start the service if deployment failed
    if (Get-Service -Name $ServiceName -ErrorAction SilentlyContinue) {
        Write-Host "Attempting to restart service..." -ForegroundColor Yellow
        Start-Service -Name $ServiceName -ErrorAction SilentlyContinue
    }

    exit 1
} finally {
    # Return to original location
    Set-Location $ScriptDir
}

Write-Host "Backend deployment script completed." -ForegroundColor Green
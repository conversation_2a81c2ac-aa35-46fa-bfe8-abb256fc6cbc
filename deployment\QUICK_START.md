# MenuOps Pricing Tool - Quick Start Guide

## Overview
This guide provides the fastest path to package and deploy the MenuOps Pricing Tool.

## For Developers: Creating Deployment Packages

### Prerequisites
- Node.js 14+ and npm
- .NET Core 3.1 SDK
- PowerShell 5.1+

### Quick Package Creation
```powershell
# Navigate to project root
cd C:\Projects\MenuOpsPricingTool

# Run the automated packaging script
.\deployment\create-packages.ps1 -Version "1.0"
```

This creates four deployment packages:
- `MenuOps-Production-v1.0.zip` - Framework-dependent production build
- `MenuOps-QA-v1.0.zip` - Framework-dependent QA build
- `MenuOps-SelfContained-v1.0.zip` - Self-contained build (no .NET runtime required)
- `MenuOps-Docker-v1.0.zip` - Docker containerization files

## For System Administrators: Server Deployment

### Option 1: Windows Server with IIS (Recommended)

#### Prerequisites
- Windows Server 2019+
- IIS with ASP.NET Core Hosting Bundle
- .NET Core 3.1 Runtime (for framework-dependent packages)

#### Quick Deployment
```powershell
# Extract package
Expand-Archive -Path "MenuOps-Production-v1.0.zip" -DestinationPath "C:\MenuOps"

# Deploy frontend
.\deploy-frontend.ps1 -Environment "production" -TargetPath "C:\inetpub\wwwroot\menuops"

# Deploy backend
.\deploy-backend.ps1 -Environment "Production" -TargetPath "C:\inetpub\wwwroot\menuops-api"
```

#### Configure IIS
1. Create website for frontend (port 80) pointing to `C:\inetpub\wwwroot\menuops`
2. Create website for backend (port 9142) pointing to `C:\inetpub\wwwroot\menuops-api`
3. Set backend application pool to "No Managed Code"

### Option 2: Self-Contained Deployment

#### Prerequisites
- Windows Server 2019+ (no .NET runtime required)

#### Quick Deployment
```powershell
# Extract self-contained package
Expand-Archive -Path "MenuOps-SelfContained-v1.0.zip" -DestinationPath "C:\MenuOps"

# Deploy frontend to IIS
Copy-Item -Path "C:\MenuOps\frontend\*" -Destination "C:\inetpub\wwwroot\menuops" -Recurse

# Run backend as Windows Service
sc create "MenuOpsAPI" binPath="C:\MenuOps\backend\AMPSMiddleWare.exe" start=auto
sc start "MenuOpsAPI"
```

### Option 3: Docker Deployment

#### Prerequisites
- Docker Desktop or Docker Engine
- Docker Compose

#### Quick Deployment
```powershell
# Extract Docker package
Expand-Archive -Path "MenuOps-Docker-v1.0.zip" -DestinationPath "C:\MenuOps\docker"
cd C:\MenuOps\docker

# Create environment file
@"
CFC_USERNAME=your_username
CFC_PASSWORD=your_password
CFC_BASE_URL=https://web.configurationcenter.com/ConfigurationCenter/8967
"@ | Out-File -FilePath ".env" -Encoding UTF8

# Deploy with Docker Compose
docker-compose up -d
```

## Configuration

### Required Configuration Changes

#### 1. Backend API Settings
Edit `appsettings.json`:
```json
{
  "CFCSettings": {
    "UserName": "YOUR_API_USERNAME",
    "Password": "YOUR_ENCRYPTED_PASSWORD",
    "BaseUrl": "https://web.configurationcenter.com/ConfigurationCenter/8967"
  }
}
```

#### 2. CORS Configuration (if needed)
Update backend CORS settings to match your domain:
```csharp
builder.WithOrigins("https://your-domain.com")
```

## Verification

### Health Checks
```powershell
# Test frontend
Invoke-WebRequest -Uri "http://localhost/health"

# Test backend
Invoke-WebRequest -Uri "http://localhost:9142/health"
```

### Application Access
- **Frontend**: `http://your-server-ip` or `https://your-domain.com`
- **Backend API**: `http://your-server-ip:9142`

## Troubleshooting

### Common Issues

#### Frontend not loading
- Check IIS website configuration
- Verify web.config is present
- Check browser console for errors

#### Backend API not responding
- Verify .NET Core runtime is installed (framework-dependent)
- Check Windows Event Log for errors
- Verify application pool configuration

#### CORS errors
- Update CORS configuration in backend
- Ensure frontend uses correct API URL
- Check protocol (HTTP/HTTPS) consistency

## Support Documentation

For detailed instructions, refer to:
- `PACKAGING_GUIDE.md` - Complete packaging instructions
- `SERVER_SETUP_GUIDE.md` - Detailed server setup and configuration
- `DEPLOYMENT_GUIDE.md` - Comprehensive deployment options and troubleshooting

## Package Contents

Each deployment package contains:
- **Frontend**: Built React application files
- **Backend**: Published .NET Core application
- **Configuration**: Template configuration files
- **Deployment Scripts**: PowerShell automation scripts
- **Documentation**: Setup and troubleshooting guides

## Security Considerations

### Production Deployment
- Install SSL certificates
- Configure HTTPS redirects
- Update CORS settings for production domains
- Secure configuration files with proper permissions
- Enable logging and monitoring

### Network Configuration
- Frontend: Port 80/443 (HTTP/HTTPS)
- Backend: Port 9142 (or configured port)
- Ensure firewall allows required ports
- Configure reverse proxy if needed

## Maintenance

### Regular Tasks
- Monitor application logs
- Update SSL certificates
- Apply security updates
- Backup configuration files
- Monitor resource usage

### Log Locations
- **IIS Logs**: `C:\inetpub\logs\LogFiles`
- **Application Logs**: Configured log directory
- **Windows Event Log**: Application and System logs
- **Docker Logs**: `docker-compose logs`

## Next Steps

After successful deployment:
1. Configure monitoring and alerting
2. Set up automated backups
3. Implement CI/CD pipeline for updates
4. Configure load balancing (if needed)
5. Set up disaster recovery procedures

For production environments, consider implementing additional security measures, performance optimization, and monitoring solutions.

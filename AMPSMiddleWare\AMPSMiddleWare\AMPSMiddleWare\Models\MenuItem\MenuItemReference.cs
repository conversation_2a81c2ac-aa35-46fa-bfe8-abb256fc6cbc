﻿using AMPSMiddleWare.Models.Owner;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.MenuItem
{
    public class MenuItemReference : OwnerReference
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Guid MenuItemUniqueID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string MenuItemID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string MenuItemName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int MenuItemPOSNumber { get; set; }
    }
}

﻿using AMPSMiddleWare.Models._BaseModels.BaseResponse;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Helpers
{
    public static class ExceptionHelper
    {

        public static void ThrowCfcApiException(string responseContent)
        {
            //if (!responseContent.TryParse(out ResponseBase responseBase))
            throw new WebCfcException(responseContent);

            //throw new CfcApiException(responseBase.Header);
        }

        public static void HandleException(object o, Exception exception)
        {
            if (exception is CfcApiException cfcException)
            {
                var statuses = cfcException.Statuses;
                var message = GenerateLogMessage(statuses);
                //Logger.Error(o, message, exception);
            }
            else
            {
                //Logger.Error(o, exception.Message, exception);
            }
        }

        public static string GenerateLogMessage(List<ResponseMessage> statuses)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < statuses.Count; i++)
            {
                sb.Append(statuses[i].ErrorCode);
                sb.Append(" - ");
                sb.Append(statuses[i].Message);
                if (i != statuses.Count - 1)
                {
                    sb.Append("\r\n");
                }
            }
            return sb.ToString();
        }
    }
}

import React, { useEffect, useState} from "react";
import Globals from '../../Globals';
import Faq from "react-faq-component"; 
import { useNavigate } from "react-router-dom";
import { Row, Col, Table, Card, CardTitle, CardBody } from "reactstrap";

import { IdleTimerProvider, useIdleTimerContext } from 'react-idle-timer/dist/index.legacy.esm.js'


const Tables = () => {
	
  const navigate = useNavigate();  
  const idleTimer = useIdleTimerContext() 
  
  useEffect(() => {   
		if(!Globals.isUserLoggedIn){
			navigate('/login');	
		}
  }, []); 
  
   const onIdle = () => {
     console.log("user is idle");
	 navigate('/login');	
  }		
	
	
  return (
  
	
		 
  
    <div>
            <Faq
                data={data}
                styles={styles}
                config={config}
            />
			
	  <IdleTimerProvider
       timeout={10000 * 60}      
       onIdle={onIdle} 
      />
			
        </div>
        
     
  );
};

const data = {
    title: "FAQ",
    rows: [
        {
            title: "Supported Template(File) Types",
            content: `Currently the tool only supports Excel templates with file extension .xlsx`,
        },
        {
            title: "Supported CFC versions",
            content:
                "TBD",
        },
        {
            title: "Can I import multiple templates for processing",
            content: `No. Currently the tool only process one file at a time.`,
        },
        {
            title: "Can I schedule file processing",
            content: `No. It is on demand.`,
        },
    ],
};

const styles = {
    // bgColor: 'white',
    titleTextColor: "blue",
    rowTitleColor: "blue",
    // rowContentColor: 'grey',
    // arrowColor: "red",
};

const config = {
    // animate: true,
    // arrowIcon: "V",
    // tabFocus: true
};


export default Tables;

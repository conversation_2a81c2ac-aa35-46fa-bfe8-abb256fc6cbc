﻿using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using AMPSMiddleWare.Settings;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models._BaseModels.BaseRequest
{
    public class RequestHeader
    {
        private readonly CFCSettings _settings;

        public RequestHeader(CFCSettings settings)
        {
            _settings = settings;
            this.ClientApplication = _settings.ClientApplication;
            this.ClientVersion = _settings.ClientVersion;
            this.ApiVersion = _settings.ApiVersion;
        }

        public string ClientApplication { get; }
        public string ClientVersion { get; }
        public string ApiVersion { get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Guid? MessageID { get; set; }
    }
}

﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMPSMiddleWare.Models;
using System.IO;
using Microsoft.Extensions.Logging;
using AMPSMiddleWare.Helpers;

namespace AMPSMiddleWare.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]

    public class CustomerConfigController : Controller
    {
        private readonly ILogger<CustomerConfigController> _logger;

        public CustomerConfigController(ILogger<CustomerConfigController> logger)
        {
            _logger = logger;
        }

        [HttpPost, Route("GetCustomerConig")]
       // [HttpGet, Route("GetCustomerConig")]
        public List<CustomerConfig> GetCustomerConig()
        {
            _logger.LogInformation("GetCustomerConig");
           
            List<CustomerConfig> lCustomers = ReadCustomerConfig.Get(_logger);

            return lCustomers;
            //return JsonConvert.SerializeObject(udata);

        }

        [HttpGet]
        public List<CustomerConfig> Get()
        {
            _logger.LogInformation("Get");

            List<CustomerConfig> lCustomers = ReadCustomerConfig.Get(_logger);

            return lCustomers;
        }

        [HttpPost, Route("Encrypt")]

        public string PriceChanges([FromBody] string requestRaw)
        {
            string encryptedString = "";

            if (!string.IsNullOrEmpty(requestRaw))
            {
                encryptedString = EncryptionHelper.EncryptAES(requestRaw);
            }

            return encryptedString;

        }

    }

    public static class ReadCustomerConfig
    {
        static List<CustomerConfig> lCustomers = null;

        public static List<CustomerConfig> Get(ILogger<CustomerConfigController> _logger)
        {
            
            if (lCustomers == null)
            {
                try
                {
                    string json = File.ReadAllText("CustomerConfig.json");
                    lCustomers = System.Text.Json.JsonSerializer.Deserialize<List<CustomerConfig>>(json);
                }
                catch
                {
                    _logger.LogError("Failed to read CustomerConfig.jason");
                }
            }

            return lCustomers;
        }

    }
}

﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models._BaseModels.BaseReference
{
    public class ReferenceBase
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int CorporatePOSNumber { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CorporateName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int StorePOSNumber { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string StoreName { get; set; }
    }
}

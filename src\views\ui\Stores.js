import React, { useState } from "react";
import Globals from '../../Globals';
import HeaderConfigs from './UiConfig.js';
import LoadingAnim from "../../assets/images/logos/loading2.gif";


import {
	<PERSON>ert,
	UncontrolledAlert,
	Card,
	CardBody,
	CardTitle, Badge, Button, ButtonGroup, Table
} from "reactstrap";


var response;

const Stores = () => {
	// For Dismiss Button with Alert
	const [visible, setVisible] = useState(true);

	const onDismiss = () => {
		setVisible(false);
	};

	const [isloadingDivVisible, setloadingDivVisibility] = useState(false)




	const getDataFromAPI = async () => {

		var headers = new Headers();
		headers.append("Authorization", "Basic " + window.btoa("MenuItemService:Natcdm900900*"));
		headers.append("Content-Type", "application/json");
		headers.append("Accept", "application/json");
		headers.append("Accept-Encoding", "gzip,deflate");
		headers.append("Access-Control-Allow-Credentials", true);
		headers.append("Access-Control-Allow-Origin", "*");
		headers.append("Access-Control-Allow-Methods", "GET,OPTIONS,PATCH,DELETE,POST,PUT");
		headers.append("Access-Control-Allow-Headers", "Content-Type");
		headers.append("Connection", "keep-alive");

		const endpoint = 'https://cors-anywhere.herokuapp.com/https://web.testconfigurationcenter.com/ConfigurationCenter/8490/ws/MenuItems/UpdateMenuItems'
		const res = await fetch(endpoint, { headers: headers })
		const data = await res.json()
		this.setState({ items: data })
	}

	const getWeather = async () => {

		let response = await fetch(Globals.BaseURL + '/weatherforecast', {




		});

		let data = await response.json();
		console.log(data);
	};

	const componentDidMount = () => {
		// Simple GET request using fetch
		fetch('https://api.npms.io/v2/search?q=react')
			.then(response => response.json())
			.then(data => this.setState({ totalReactPackages: data.total }));
		console.log("componentDidMount ::");
	}


	const getWeather1 = () => {

		/*
	   let myObject = JSON.parse(Globals.TemplateData, (key, value) => {
			return value;
	   });			   
	   */

		var myObject = JSON.parse(Globals.TemplateData)

		for (let i = 0; i < myObject.length; i++) {
			console.log(myObject[i].UniqueID);
		}



		//console.log(JSON.stringify(myObject("UniqueID")));
		//console.log(myObject("UniqueID"));

		// fetch(Globals.BaseURL+'/weatherforecast')
		//.then( response => response.json())
		//console.log(response);


	}

	const PriceChanges1 = async () => {



		let response = await fetch(Globals.BaseURL + '/PriceChanges1', {
			method: 'POST',
			body: Globals.TemplateData,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate'
			},
		});
		let data = await response.json();
		console.log(data);

	};



	const priceChange = async () => {

		setloadingDivVisibility(!isloadingDivVisible);

		var myObject = JSON.parse(Globals.TemplateData);

		for (let i = 0; i < myObject.length; i++) {
			console.log(myObject[i].UniqueID);
		}

		var jsonTxt = "{ Header";
		jsonTxt += ":";
		jsonTxt += "{";
		jsonTxt += "ClientApplication " + " : " + HeaderConfigs.ClientApplication + " , ";
		jsonTxt += "ClientVersion " + " : " + HeaderConfigs.ClientVersion + " , ";
		jsonTxt += "ApiVersion " + " : " + HeaderConfigs.ApiVersion + " , ";
		jsonTxt += "MessageID " + " : " + HeaderConfigs.MessageID + " , ";
		jsonTxt += "CorrelationID " + " : " + "PriceChange" + " , ";
		jsonTxt += "Accept " + " : " + "application/json" + " , ";
		jsonTxt += "Connection " + " : " + "keep-alive";

		jsonTxt += " },";
		jsonTxt += "Batch";
		jsonTxt += " : ";
		jsonTxt += " [ ";
		jsonTxt = "";

		for (let i = 0; i < myObject.length; i++) {
			jsonTxt += "{" + '"MenuItemUniqueID"' + ":" + myObject[i].UniqueID + "," + '"Price"' + ":" + myObject[i].Price + "}"

			if (i < myObject.length - 1) {
				jsonTxt += ","
			}
		}
		//jsonTxt += "]}";
		jsonTxt = Globals.TemplateData;
		console.log(JSON.stringify(jsonTxt));
		let response = await fetch(Globals.BaseURL + '/PriceChanges', {
			method: 'POST',
			body:

				jsonTxt

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',
				'Authorization': 'Basic ' + window.btoa("MenuItemService:Natcdm900900*"),
			},
		});

		let data = await response.json();
		console.log(response.status);
		if (response.status === 200) {
			setloadingDivVisibility(false);

		}


		console.log(data);

	};


	const storeList = async () => {


		var jsonTxt = "{ Header";
		jsonTxt += ":";
		jsonTxt += "{";
		jsonTxt += "ClientApplication " + " : " + "AMPS" + " , ";
		jsonTxt += "ClientVersion " + " : " + "*******" + " , ";
		jsonTxt += "ApiVersion " + " : " + "*********" + " , ";
		jsonTxt += "MessageID " + " : " + "e22b1623-7962-4daf-a2a2-1b832fb6a59f"; //+ " , ";
		//jsonTxt += "CorrelationID " + " : " + "StoreList" + " , ";
		//jsonTxt += "Accept " + " : " + "application/json" + " , ";
		//jsonTxt += "Connection " + " : " + "keep-alive" ;

		jsonTxt += " }";
		jsonTxt += " }";



		jsonTxt = JSON.stringify(jsonTxt);

		alert(jsonTxt)

		let response = await fetch(Globals.BaseURL + '/StoreList', {
			method: 'POST',
			body:

				jsonTxt

			,
			headers: {
				'Content-type': 'application/json; charset=UTF-8',
				'Accept': 'application/json',
				'Accept-Encoding': 'gzip,deflate',
				'Authorization': 'Basic ' + window.btoa("MenuItemService:P@88w0rd"),
			},
		});

		let data = await response.json();
		console.log(response.status);
		if (response.status === 200) {
			setloadingDivVisibility(false);

		}


		console.log(data);

	};

	return (





		<div>

			<h1>
				<Badge color="secondary">CFC Stores</Badge>
			</h1>

			<Card>
				<CardTitle tag="h6" className="border-bottom p-3 mb-0">
					<b>Store List</b>
				</CardTitle>
				<CardBody className="">
					<div className="mt-3">

						<Table className="no-wrap mt-0 align-middle" responsive bordered hover>
							<thead>
								<tr>
									<th>Store Name</th>
									<th>POS#</th>
									<th>StoreUniqueID</th>
									<th>CFCOwnerID</th>
								</tr>
							</thead>

						</Table>

					</div>
				</CardBody>
			</Card>


			<div id="loadingDiv" style={{ display: (isloadingDivVisible) ? 'block' : 'none' }}>
				<br />
				<br />
				<Alert color="danger"> Please wait while we load the data</Alert>
				<br />
				<img src={LoadingAnim} style={{ "position": "relative", top: -220 }} />
			</div>


			<ButtonGroup>

				<Button onClick={() => { storeList(); }} color="secondary"><b>Apply</b></Button>

			</ButtonGroup>


		</div>
	);
};

export default Stores;

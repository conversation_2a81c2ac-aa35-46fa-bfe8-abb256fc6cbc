﻿using AMPSMiddleWare.Models._BaseModels.BaseRequest;
using AMPSMiddleWare.Models.MenuItem;
using AMPSMiddleWare.Settings;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.Promotion
{
    public class GetPromotionsReq
    {
        public GetPromotionsReq(CFCSettings settings)
        {
            this.Header = new RequestHeader(settings);
        }

        public RequestHeader Header { get; set; }
        public List<PromotionReference> References { get; set; }
    }
}

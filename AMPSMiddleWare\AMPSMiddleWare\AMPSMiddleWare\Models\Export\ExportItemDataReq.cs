﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using AMPSMiddleWare.Settings;
using AMPSMiddleWare.Models._BaseModels.BaseRequest;


namespace AMPSMiddleWare.Models.Export
{   
    
    public class ExportItemDataReq
    {
        
        public ExportItemDataReq(CFCSettings settings)
        {
            this.Header = new RequestHeader(settings);
        }
        public RequestHeader Header { get; set; }
        public List<Reference> References { get; set; }
        
        public class Reference
        {
            public string MenuItemUniqueID { get; set; }            
        }
                
    }
}





﻿using System.Collections.Generic;
using AMPSMiddleWare.Models.UserDB;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using System;
using Microsoft.Extensions.Logging;

namespace AMPSMiddleWare.Controllers
{

    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]

    public class USERController : ControllerBase
    {
        
        private readonly ILogger<USERController> _logger;

        public USERController(ILogger <USERController> logger)
        {
            _logger = logger;
        }

        // POST api/<UserController>
        [HttpPost, Route("GetUser")]
        public string GetUser([FromBody] dynamic value)
        {
            _logger.LogInformation("GetUser");
            string sValue = System.Text.Json.JsonSerializer.Serialize(value);
            var values = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(sValue);

            string sReturn = "false";

            ReadAndParseJsonFile rp = new ReadAndParseJsonFile();

            var lUsers = rp.GetUserDB();

            foreach (var item in lUsers)
            {
                string sitem = item.UserName;
                string spass = item.UserPassword;

                if (values.ContainsValue(sitem))
                {
                    if (values["Password"].Equals(spass) && values["Username"].Equals(sitem))
                    {
                        sReturn = "true";
                    }
                }

            }
       
            return System.Text.Json.JsonSerializer.Serialize(sReturn);
        }

        // POST api/<UserController>
        [HttpPost, Route("GetUserList")]
        public string GetUserList()
        {
            _logger.LogInformation("GetUserList");
            ReadAndParseJsonFile rp = new ReadAndParseJsonFile();

            List<UserDB> udata = rp.GetUserDB();

            return JsonConvert.SerializeObject(udata);

        }

        [HttpPost, Route("GetURLS")]
        public List<URLS> GetURLS()
        {
            _logger.LogInformation("GetURLS");
            // Build configuration
            IConfigurationRoot _config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetParent(AppContext.BaseDirectory).FullName)
                .AddJsonFile("appsettings.json", false)
                .Build();

            List<URLS> durls = _config.GetSection("CFCUrls:URLS").Get<List<URLS>>();

            return durls;
        }

        [HttpPost, Route("GetURLSByName")]
        public Dictionary<string, string> GetURLByName([FromBody] dynamic value)
        {
            _logger.LogInformation("GetURLSByName");
            string sValue = System.Text.Json.JsonSerializer.Serialize(value);
            var values = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(sValue);

            // Build configuration
            IConfigurationRoot _config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetParent(AppContext.BaseDirectory).FullName)
                .AddJsonFile("appsettings.json", false)
                .Build();

            List<URLS> lurls = _config.GetSection("CFCUrls:URLS").Get<List<URLS>>();

            Dictionary<string, string> sRet = new Dictionary<string, string>();


            foreach (var item in lurls)
            {
                if (item.Name.Equals(values["name"]))
                {

                    return item.Env;

                }
            }

            return sRet;
        }

        [HttpPost, Route("GetURLByNameAndEnv")]
        public string GetURLByNameAndEnv([FromBody] dynamic value)
        {
            _logger.LogInformation("GetURLByNameAndEnv");
            string sValue = System.Text.Json.JsonSerializer.Serialize(value);
            var values = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(sValue);

            // Build configuration
            IConfigurationRoot _config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetParent(AppContext.BaseDirectory).FullName)
                .AddJsonFile("appsettings.json", false)
                .Build();

            List<URLS> lurls = _config.GetSection("CFCUrls:URLS").Get<List<URLS>>();

            string sRet = "";

            foreach (var item in lurls)
            {
                if (item.Name.Equals(values["name"]))
                {

                    string etype = values["env"];

                    foreach (var obj in item.Env)
                    {
                        if (etype.Equals(obj.Key))
                        {
                            sRet = obj.Value;
                        }

                    }

                }
            }

            return sRet;
        }

    }

    public class ReadAndParseJsonFile
    {

        public List<UserDB> GetUserDB()
        {
            string json = File.ReadAllText("ampsusers.json");

            List<UserDB> lusers = System.Text.Json.JsonSerializer.Deserialize<List<UserDB>>(json);

            return lusers;
        }

    }

}

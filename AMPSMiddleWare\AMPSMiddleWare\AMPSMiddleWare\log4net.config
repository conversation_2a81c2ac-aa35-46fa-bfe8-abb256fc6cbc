﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  
  <!-- Middelware logger-->
  <appender name="RollingFile" type="log4net.Appender.RollingFileAppender">
    <file value="./Logs/AMPSMiddleWare.log" />
    <appendToFile value="true" />
    <maximumFileSize value="2MB" />
    <maxSizeRollBackups value="2" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%utcdate %logger{1} (%property{Category}) (%property{Session}) %5level  %message%newline %exception" />      
    </layout>
  </appender>
  
  <!-- Client logger-->
  <logger name="ClientLogger">
    <level  value ="ALL" />
    <appender-ref ref="ClientRollingFile" />
  </logger>
  
  <appender name="ClientRollingFile" type="log4net.Appender.RollingFileAppender">
    <file value="./Logs/AMPSClient.log" />    
    <appendToFile value="true" />
    <maximumFileSize value="2MB" />
    <maxSizeRollBackups value="2" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%utcdate %logger{1} (%property{ClientVersion}) (%property{Session}) %5level  %message%newline %exception" />
    </layout>
  </appender>
  
  <root>
    <level value="ALL"/>
    <appender-ref ref="RollingFile" />
  </root>
  
</log4net>

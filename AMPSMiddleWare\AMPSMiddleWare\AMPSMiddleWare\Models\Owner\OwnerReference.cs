﻿using AMPSMiddleWare.Models._BaseModels.BaseReference;
using Newtonsoft.Json;
using System;

namespace AMPSMiddleWare.Models.Owner
{
    public class OwnerReference : ReferenceBase
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Guid OwnerUniqueID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string StoreGroupName { get; set; }
    }
}

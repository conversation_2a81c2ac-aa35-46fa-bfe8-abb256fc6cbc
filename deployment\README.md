# MenuOps Pricing Tool - Deployment Guide

## Overview
This guide provides comprehensive instructions for deploying the MenuOps Pricing Tool, which consists of:
- **Frontend**: React 17.0.2 application
- **Backend**: ASP.NET Core 3.1 middleware

## Prerequisites

### Development Environment
- Node.js 14+ and npm
- .NET Core 3.1 SDK
- Git

### Production Environment
- Windows Server 2019+ or Linux Ubuntu 20.04+
- IIS (Windows) or Nginx (Linux)
- .NET Core 3.1 Runtime
- SSL Certificate

## Quick Start

### 1. Clone Repository
```bash
git clone <repository-url>
cd MenuOpsPricingTool
```

### 2. Frontend Setup
```bash
# Install dependencies
npm install

# Build for production
npm run build-prod

# Build for QA
npm run build-qa
```

### 3. Backend Setup
```bash
cd AMPSMiddleWare/AMPSMiddleWare
dotnet restore
dotnet publish -c Release -o ./publish
```

## Environment Configuration

### Frontend Environments
The application supports multiple environments configured in `.env-cmdrc`:

- **Development**: `http://localhost:9142`
- **Production**: `http://**************:9142`
- **QA**: `http://**************:9143`

### Backend Configuration
Update `appsettings.json` for each environment:

```json
{
  "AllowedHosts": "*",
  "CFCSettings": {
    "UserName": "APIUser",
    "Password": "[ENCRYPTED_PASSWORD]",
    "BaseUrl": "https://web.configurationcenter.com/ConfigurationCenter/8967"
  }
}
```

## Deployment Options

### Option 1: Traditional IIS Deployment (Windows)

#### Frontend Deployment
1. Build the React application:
   ```bash
   npm run build-prod
   ```

2. Copy `build` folder contents to IIS website directory

3. Configure IIS with URL Rewrite for SPA:
   ```xml
   <rule name="React Routes" stopProcessing="true">
     <match url=".*" />
     <conditions logicalGrouping="MatchAll">
       <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
       <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
     </conditions>
     <action type="Rewrite" url="/" />
   </rule>
   ```

#### Backend Deployment
1. Publish the ASP.NET Core application:
   ```bash
   dotnet publish -c Release -o ./publish
   ```

2. Copy published files to IIS application directory

3. Install ASP.NET Core Hosting Bundle on server

4. Configure IIS application pool for .NET Core

### Option 2: Docker Deployment

#### Frontend Dockerfile
```dockerfile
FROM nginx:alpine
COPY build/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

#### Backend Dockerfile
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:3.1
WORKDIR /app
COPY publish/ .
EXPOSE 80
ENTRYPOINT ["dotnet", "AMPSMiddleWare.dll"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "80:80"

  backend:
    build: ./backend
    ports:
      - "9142:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
```

### Option 3: Cloud Deployment (Azure)

#### Frontend - Azure Static Web Apps
1. Build application: `npm run build-prod`
2. Deploy to Azure Static Web Apps
3. Configure custom domain and SSL

#### Backend - Azure App Service
1. Publish application: `dotnet publish`
2. Deploy to Azure App Service
3. Configure application settings
4. Enable Application Insights

## Security Configuration

### SSL/HTTPS Setup
1. Obtain SSL certificate (Let's Encrypt recommended)
2. Configure HTTPS redirect in web server
3. Update CORS settings in backend:
   ```csharp
   builder.WithOrigins("https://yourdomain.com")
   ```

### Environment Variables
Store sensitive configuration in environment variables:
- `CFC_USERNAME`
- `CFC_PASSWORD`
- `CFC_BASE_URL`

## Monitoring Setup

### Health Checks
Add health check endpoints:

#### Frontend Health Check
Create `/health` endpoint returning:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

#### Backend Health Check
Configure in `Startup.cs`:
```csharp
services.AddHealthChecks();
app.UseHealthChecks("/health");
```

### Logging Configuration
Update `log4net.config` for production:
```xml
<appender name="FileAppender" type="log4net.Appender.FileAppender">
  <file value="logs/application.log" />
  <appendToFile value="true" />
  <layout type="log4net.Layout.PatternLayout">
    <conversionPattern value="%date [%thread] %level %logger - %message%newline" />
  </layout>
</appender>
```

## Troubleshooting

### Common Issues

#### Frontend Issues
- **Build Fails**: Check Node.js version compatibility
- **API Connection**: Verify CORS configuration
- **Routing Issues**: Ensure URL rewrite rules are configured

#### Backend Issues
- **Application Won't Start**: Check .NET Core runtime installation
- **CORS Errors**: Verify allowed origins in Startup.cs
- **Configuration Issues**: Check appsettings.json format

### Log Locations
- **Frontend**: Browser console and network tab
- **Backend**: `logs/application.log` or Windows Event Log
- **IIS**: IIS logs in `C:\inetpub\logs\LogFiles`

## Maintenance

### Regular Tasks
- Monitor application logs
- Update SSL certificates
- Apply security updates
- Monitor resource usage
- Backup configuration files

### Performance Optimization
- Enable gzip compression
- Configure CDN for static assets
- Optimize database queries
- Monitor memory usage

## Support Contacts
- **Development Team**: [team-email]
- **Infrastructure Team**: [infra-email]
- **Emergency Contact**: [emergency-contact]
﻿using AMPSMiddleWare.Settings;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models._BaseModels.BaseRequest
{
    public class RequestBase
    {
        private readonly CFCSettings _settings;

        public RequestBase(CFCSettings settings)
        {
            _settings = settings;
        }
        public RequestHeader Header { get { return new RequestHeader(_settings); } }
    }
}

version: '3.8'

services:
  # Frontend - React Application
  frontend:
    build:
      context: ..
      dockerfile: deployment/Dockerfile.frontend
    container_name: menuops-frontend
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - menuops-network

  # Backend - ASP.NET Core API
  backend:
    build:
      context: ..
      dockerfile: deployment/Dockerfile.backend
    container_name: menuops-backend
    ports:
      - "9142:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - CFC_USERNAME=${CFC_USERNAME}
      - CFC_PASSWORD=${CFC_PASSWORD}
      - CFC_BASE_URL=${CFC_BASE_URL}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    networks:
      - menuops-network

  # Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: menuops-proxy
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - menuops-network

networks:
  menuops-network:
    driver: bridge

volumes:
  logs:
  data:
# MenuOps Pricing Tool - Azure Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the MenuOps Pricing Tool to Microsoft Azure using various Azure services.

## Architecture Options

### Option 1: Azure App Service (Recommended)
- **Frontend**: Azure Static Web Apps
- **Backend**: Azure App Service (Linux or Windows)
- **Storage**: Azure Blob Storage for files
- **Monitoring**: Application Insights

### Option 2: Azure Container Instances
- **Frontend**: Azure Container Instances with Nginx
- **Backend**: Azure Container Instances with .NET Core
- **Storage**: Azure File Share
- **Monitoring**: Azure Monitor

### Option 3: Azure Kubernetes Service (AKS)
- **Frontend**: Kubernetes deployment with Nginx ingress
- **Backend**: Kubernetes deployment with .NET Core
- **Storage**: Azure Disk/File Storage
- **Monitoring**: Azure Monitor for containers

## Prerequisites

### Azure Account Setup
- Active Azure subscription
- Azure CLI installed locally
- PowerShell 7+ or Azure Cloud Shell
- Docker Desktop (for container deployments)

### Required Azure Services
- Azure App Service
- Azure Static Web Apps
- Azure Key Vault (for secrets)
- Azure Application Insights
- Azure Blob Storage
- Azure CDN (optional)

## Option 1: Azure App Service Deployment (Recommended)

### Step 1: Create Azure Resources

#### 1.1 Create Resource Group
```powershell
# Login to Azure
az login

# Create resource group
az group create --name "rg-menuops-prod" --location "East US"
```

#### 1.2 Create App Service Plan
```powershell
# Create App Service Plan (Linux)
az appservice plan create `
  --name "asp-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --sku "B2" `
  --is-linux
```

#### 1.3 Create Backend App Service
```powershell
# Create App Service for backend
az webapp create `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --plan "asp-menuops-prod" `
  --runtime "DOTNETCORE:3.1"
```

#### 1.4 Create Static Web App for Frontend
```powershell
# Create Static Web App
az staticwebapp create `
  --name "swa-menuops-frontend-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US 2"
```

### Step 2: Configure Azure Key Vault

#### 2.1 Create Key Vault
```powershell
# Create Key Vault
az keyvault create `
  --name "kv-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US"
```

#### 2.2 Store Secrets
```powershell
# Store CFC credentials
az keyvault secret set `
  --vault-name "kv-menuops-prod" `
  --name "CFC-Username" `
  --value "APIUser"

az keyvault secret set `
  --vault-name "kv-menuops-prod" `
  --name "CFC-Password" `
  --value "your-encrypted-password"

az keyvault secret set `
  --vault-name "kv-menuops-prod" `
  --name "CFC-BaseUrl" `
  --value "https://web.configurationcenter.com/ConfigurationCenter/8967"
```

### Step 3: Configure Backend App Service

#### 3.1 Enable Managed Identity
```powershell
# Enable system-assigned managed identity
az webapp identity assign `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod"
```

#### 3.2 Grant Key Vault Access
```powershell
# Get the principal ID
$principalId = az webapp identity show `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --query principalId --output tsv

# Grant access to Key Vault
az keyvault set-policy `
  --name "kv-menuops-prod" `
  --object-id $principalId `
  --secret-permissions get list
```

#### 3.3 Configure Application Settings
```powershell
# Configure app settings with Key Vault references
az webapp config appsettings set `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --settings `
    "CFCSettings__UserName=@Microsoft.KeyVault(VaultName=kv-menuops-prod;SecretName=CFC-Username)" `
    "CFCSettings__Password=@Microsoft.KeyVault(VaultName=kv-menuops-prod;SecretName=CFC-Password)" `
    "CFCSettings__BaseUrl=@Microsoft.KeyVault(VaultName=kv-menuops-prod;SecretName=CFC-BaseUrl)" `
    "ASPNETCORE_ENVIRONMENT=Production"
```

### Step 4: Deploy Backend Application

#### 4.1 Prepare Deployment Package
```powershell
# Navigate to backend project
cd AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare

# Publish application
dotnet publish -c Release -o ./publish

# Create deployment package
Compress-Archive -Path "./publish/*" -DestinationPath "../../../menuops-backend.zip"
```

#### 4.2 Deploy to App Service
```powershell
# Deploy using Azure CLI
az webapp deployment source config-zip `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --src "menuops-backend.zip"
```

### Step 5: Deploy Frontend Application

#### 5.1 Update Frontend Configuration
Create `staticwebapp.config.json`:
```json
{
  "routes": [
    {
      "route": "/api/*",
      "rewrite": "https://app-menuops-api-prod.azurewebsites.net/api/*"
    },
    {
      "route": "/*",
      "serve": "/index.html",
      "statusCode": 200
    }
  ],
  "responseOverrides": {
    "401": {
      "redirect": "/login",
      "statusCode": 302
    }
  }
}
```

#### 5.2 Build and Deploy Frontend
```powershell
# Update environment configuration
$env:REACT_APP_SERVER_URL = "https://app-menuops-api-prod.azurewebsites.net"

# Build for production
npm run build-prod

# Deploy to Static Web App (using GitHub Actions or Azure CLI)
az staticwebapp environment set `
  --name "swa-menuops-frontend-prod" `
  --environment-name "default" `
  --source "./build"
```

### Step 6: Configure Custom Domain and SSL

#### 6.1 Configure Custom Domain
```powershell
# Add custom domain to Static Web App
az staticwebapp hostname set `
  --name "swa-menuops-frontend-prod" `
  --resource-group "rg-menuops-prod" `
  --hostname "menuops.yourdomain.com"

# Add custom domain to App Service
az webapp config hostname add `
  --webapp-name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --hostname "api.menuops.yourdomain.com"
```

#### 6.2 Configure SSL Certificates
```powershell
# Enable HTTPS only
az webapp update `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --https-only true
```

## Option 2: Azure Container Instances Deployment

### Step 1: Create Container Registry
```powershell
# Create Azure Container Registry
az acr create `
  --name "acrmenuopsprod" `
  --resource-group "rg-menuops-prod" `
  --sku "Basic" `
  --admin-enabled true
```

### Step 2: Build and Push Container Images

#### 2.1 Build Backend Container
```powershell
# Login to ACR
az acr login --name "acrmenuopsprod"

# Build and push backend image
docker build -f deployment/Dockerfile.backend -t acrmenuopsprod.azurecr.io/menuops-backend:latest .
docker push acrmenuopsprod.azurecr.io/menuops-backend:latest
```

#### 2.2 Build Frontend Container
```powershell
# Build and push frontend image
docker build -f deployment/Dockerfile.frontend -t acrmenuopsprod.azurecr.io/menuops-frontend:latest .
docker push acrmenuopsprod.azurecr.io/menuops-frontend:latest
```

### Step 3: Deploy Container Instances

#### 3.1 Create Backend Container Instance
```powershell
# Get ACR credentials
$acrPassword = az acr credential show --name "acrmenuopsprod" --query "passwords[0].value" --output tsv

# Create backend container instance
az container create `
  --name "aci-menuops-backend-prod" `
  --resource-group "rg-menuops-prod" `
  --image "acrmenuopsprod.azurecr.io/menuops-backend:latest" `
  --registry-login-server "acrmenuopsprod.azurecr.io" `
  --registry-username "acrmenuopsprod" `
  --registry-password $acrPassword `
  --dns-name-label "menuops-api-prod" `
  --ports 80 `
  --environment-variables `
    "ASPNETCORE_ENVIRONMENT=Production" `
    "CFCSettings__UserName=APIUser" `
    "CFCSettings__BaseUrl=https://web.configurationcenter.com/ConfigurationCenter/8967"
```

#### 3.2 Create Frontend Container Instance
```powershell
# Create frontend container instance
az container create `
  --name "aci-menuops-frontend-prod" `
  --resource-group "rg-menuops-prod" `
  --image "acrmenuopsprod.azurecr.io/menuops-frontend:latest" `
  --registry-login-server "acrmenuopsprod.azurecr.io" `
  --registry-username "acrmenuopsprod" `
  --registry-password $acrPassword `
  --dns-name-label "menuops-frontend-prod" `
  --ports 80 443
```

## Monitoring and Logging

### Step 1: Configure Application Insights
```powershell
# Create Application Insights
az monitor app-insights component create `
  --app "ai-menuops-prod" `
  --location "East US" `
  --resource-group "rg-menuops-prod" `
  --application-type web

# Get instrumentation key
$instrumentationKey = az monitor app-insights component show `
  --app "ai-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --query instrumentationKey --output tsv

# Configure App Service to use Application Insights
az webapp config appsettings set `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --settings "APPINSIGHTS_INSTRUMENTATIONKEY=$instrumentationKey"
```

### Step 2: Configure Log Analytics
```powershell
# Create Log Analytics workspace
az monitor log-analytics workspace create `
  --workspace-name "law-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US"
```

## Security Configuration

### Step 1: Configure Network Security
```powershell
# Create Virtual Network (for enhanced security)
az network vnet create `
  --name "vnet-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --address-prefix "10.0.0.0/16" `
  --subnet-name "subnet-app" `
  --subnet-prefix "********/24"

# Configure VNet integration for App Service
az webapp vnet-integration add `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --vnet "vnet-menuops-prod" `
  --subnet "subnet-app"
```

### Step 2: Configure Azure Front Door (Optional)
```powershell
# Create Front Door for global load balancing and WAF
az network front-door create `
  --name "fd-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --backend-address "app-menuops-api-prod.azurewebsites.net"
```

## Backup and Disaster Recovery

### Step 1: Configure App Service Backup
```powershell
# Create storage account for backups
az storage account create `
  --name "stmenuopsbackupprod" `
  --resource-group "rg-menuops-prod" `
  --location "East US" `
  --sku "Standard_LRS"

# Configure automatic backup
az webapp config backup update `
  --webapp-name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --container-url "https://stmenuopsbackupprod.blob.core.windows.net/backups" `
  --frequency 1 `
  --retain-one true `
  --retention 30
```

## Cost Optimization

### Recommended SKUs by Environment:
- **Development**: B1 App Service Plan, Basic Key Vault
- **QA**: B2 App Service Plan, Standard Key Vault
- **Production**: S2 App Service Plan, Premium Key Vault

### Auto-scaling Configuration:
```powershell
# Configure auto-scaling for App Service
az monitor autoscale create `
  --name "autoscale-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --resource "/subscriptions/{subscription-id}/resourceGroups/rg-menuops-prod/providers/Microsoft.Web/serverfarms/asp-menuops-prod" `
  --min-count 1 `
  --max-count 3 `
  --count 1
```

## Deployment Automation

### Azure DevOps Pipeline (azure-pipelines.yml):
```yaml
trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

variables:
  buildConfiguration: 'Release'
  azureSubscription: 'your-service-connection'

stages:
- stage: Build
  jobs:
  - job: BuildBackend
    steps:
    - task: DotNetCoreCLI@2
      inputs:
        command: 'publish'
        publishWebProjects: true
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)'
    
  - job: BuildFrontend
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '16.x'
    - script: |
        npm install
        npm run build-prod
      displayName: 'Build React App'

- stage: Deploy
  jobs:
  - deployment: DeployBackend
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            inputs:
              azureSubscription: '$(azureSubscription)'
              appName: 'app-menuops-api-prod'
              package: '$(Pipeline.Workspace)/**/*.zip'
```

## Troubleshooting

### Common Issues:
1. **Key Vault Access**: Ensure Managed Identity has proper permissions
2. **CORS Errors**: Update CORS settings in backend for Azure domains
3. **Static Web App Routing**: Verify staticwebapp.config.json configuration
4. **SSL Certificate**: Ensure custom domains are properly configured

### Monitoring Commands:
```powershell
# Check App Service logs
az webapp log tail --name "app-menuops-api-prod" --resource-group "rg-menuops-prod"

# Check container instance logs
az container logs --name "aci-menuops-backend-prod" --resource-group "rg-menuops-prod"

# Check Application Insights metrics
az monitor metrics list --resource "/subscriptions/{subscription-id}/resourceGroups/rg-menuops-prod/providers/Microsoft.Web/sites/app-menuops-api-prod"
```

This Azure deployment provides enterprise-grade hosting with built-in security, monitoring, and scalability features suitable for production workloads.

## Environment-Specific Configurations

### Development Environment
```powershell
# Create development resource group
az group create --name "rg-menuops-dev" --location "East US"

# Create development App Service (smaller SKU)
az appservice plan create `
  --name "asp-menuops-dev" `
  --resource-group "rg-menuops-dev" `
  --sku "F1" `
  --is-linux

az webapp create `
  --name "app-menuops-api-dev" `
  --resource-group "rg-menuops-dev" `
  --plan "asp-menuops-dev" `
  --runtime "DOTNETCORE:3.1"

# Configure development settings
az webapp config appsettings set `
  --name "app-menuops-api-dev" `
  --resource-group "rg-menuops-dev" `
  --settings `
    "ASPNETCORE_ENVIRONMENT=Development" `
    "CFCSettings__BaseUrl=https://web.configurationcenter.com/ConfigurationCenter/amps-lab-test-Popeyes"
```

### QA Environment
```powershell
# Create QA resource group
az group create --name "rg-menuops-qa" --location "East US"

# Create QA App Service
az appservice plan create `
  --name "asp-menuops-qa" `
  --resource-group "rg-menuops-qa" `
  --sku "B1" `
  --is-linux

az webapp create `
  --name "app-menuops-api-qa" `
  --resource-group "rg-menuops-qa" `
  --plan "asp-menuops-qa" `
  --runtime "DOTNETCORE:3.1"

# Configure QA settings
az webapp config appsettings set `
  --name "app-menuops-api-qa" `
  --resource-group "rg-menuops-qa" `
  --settings `
    "ASPNETCORE_ENVIRONMENT=QA" `
    "CFCSettings__BaseUrl=https://web.configurationcenter.com/ConfigurationCenter/amps-lab-PreProd-Popeyes"
```

## Azure Storage Configuration

### Step 1: Create Storage Account for File Management
```powershell
# Create storage account
az storage account create `
  --name "stmenuopsprod" `
  --resource-group "rg-menuops-prod" `
  --location "East US" `
  --sku "Standard_LRS" `
  --kind "StorageV2"

# Create containers for different file types
az storage container create `
  --name "excel-templates" `
  --account-name "stmenuopsprod" `
  --public-access off

az storage container create `
  --name "user-uploads" `
  --account-name "stmenuopsprod" `
  --public-access off

az storage container create `
  --name "export-files" `
  --account-name "stmenuopsprod" `
  --public-access off

# Get storage connection string
$storageConnectionString = az storage account show-connection-string `
  --name "stmenuopsprod" `
  --resource-group "rg-menuops-prod" `
  --query connectionString --output tsv

# Add storage connection to app settings
az webapp config appsettings set `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --settings "AzureStorage__ConnectionString=$storageConnectionString"
```

### Step 2: Configure File Upload Handling
Update the backend to use Azure Blob Storage instead of local file system:

```csharp
// Add to appsettings.json
{
  "AzureStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=stmenuopsprod;AccountKey=...",
    "ContainerNames": {
      "ExcelTemplates": "excel-templates",
      "UserUploads": "user-uploads",
      "ExportFiles": "export-files"
    }
  }
}
```

## Database Migration Strategy

### Option 1: Migrate to Azure SQL Database
```powershell
# Create Azure SQL Server
az sql server create `
  --name "sql-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US" `
  --admin-user "menuopsadmin" `
  --admin-password "YourSecurePassword123!"

# Create Azure SQL Database
az sql db create `
  --name "db-menuops-prod" `
  --server "sql-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --service-objective "S1"

# Configure firewall rules
az sql server firewall-rule create `
  --name "AllowAzureServices" `
  --server "sql-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --start-ip-address "0.0.0.0" `
  --end-ip-address "0.0.0.0"

# Get connection string
$sqlConnectionString = az sql db show-connection-string `
  --name "db-menuops-prod" `
  --server "sql-menuops-prod" `
  --client ado.net --output tsv

# Add to app settings
az webapp config appsettings set `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --settings "ConnectionStrings__DefaultConnection=$sqlConnectionString"
```

### Option 2: Use Azure Cosmos DB (NoSQL)
```powershell
# Create Cosmos DB account
az cosmosdb create `
  --name "cosmos-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --kind "GlobalDocumentDB" `
  --default-consistency-level "Session"

# Create database and containers
az cosmosdb sql database create `
  --account-name "cosmos-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --name "MenuOpsDB"

az cosmosdb sql container create `
  --account-name "cosmos-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --database-name "MenuOpsDB" `
  --name "Users" `
  --partition-key-path "/userId"

az cosmosdb sql container create `
  --account-name "cosmos-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --database-name "MenuOpsDB" `
  --name "CustomerConfigs" `
  --partition-key-path "/customerId"
```

## Advanced Security Configuration

### Step 1: Configure Azure Active Directory Authentication
```powershell
# Create Azure AD App Registration
az ad app create `
  --display-name "MenuOps Pricing Tool" `
  --homepage "https://menuops.yourdomain.com" `
  --reply-urls "https://menuops.yourdomain.com/.auth/login/aad/callback"

# Get App ID
$appId = az ad app list --display-name "MenuOps Pricing Tool" --query "[0].appId" --output tsv

# Configure App Service Authentication
az webapp auth update `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --enabled true `
  --action LoginWithAzureActiveDirectory `
  --aad-client-id $appId `
  --aad-token-issuer-url "https://sts.windows.net/{tenant-id}/"
```

### Step 2: Configure Web Application Firewall
```powershell
# Create Application Gateway with WAF
az network application-gateway create `
  --name "agw-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US" `
  --capacity 2 `
  --sku "WAF_v2" `
  --vnet-name "vnet-menuops-prod" `
  --subnet "subnet-agw" `
  --public-ip-address "pip-menuops-agw" `
  --servers "app-menuops-api-prod.azurewebsites.net"

# Configure WAF policy
az network application-gateway waf-policy create `
  --name "waf-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US"

# Apply WAF policy to Application Gateway
az network application-gateway waf-policy apply `
  --gateway-name "agw-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --policy-name "waf-menuops-prod"
```

## Performance Optimization

### Step 1: Configure Azure CDN
```powershell
# Create CDN profile
az cdn profile create `
  --name "cdn-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US" `
  --sku "Standard_Microsoft"

# Create CDN endpoint for static content
az cdn endpoint create `
  --name "menuops-static" `
  --profile-name "cdn-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --origin "swa-menuops-frontend-prod.azurestaticapps.net" `
  --origin-host-header "swa-menuops-frontend-prod.azurestaticapps.net"
```

### Step 2: Configure Redis Cache
```powershell
# Create Redis Cache for session management
az redis create `
  --name "redis-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --location "East US" `
  --sku "Basic" `
  --vm-size "C1"

# Get Redis connection string
$redisConnectionString = az redis list-keys `
  --name "redis-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --query primaryKey --output tsv

# Add to app settings
az webapp config appsettings set `
  --name "app-menuops-api-prod" `
  --resource-group "rg-menuops-prod" `
  --settings "Redis__ConnectionString=redis-menuops-prod.redis.cache.windows.net:6380,password=$redisConnectionString,ssl=True,abortConnect=False"
```

## Disaster Recovery and High Availability

### Step 1: Configure Geo-Redundancy
```powershell
# Create secondary region resources
az group create --name "rg-menuops-prod-dr" --location "West US 2"

# Create secondary App Service
az appservice plan create `
  --name "asp-menuops-prod-dr" `
  --resource-group "rg-menuops-prod-dr" `
  --sku "S2" `
  --is-linux

az webapp create `
  --name "app-menuops-api-prod-dr" `
  --resource-group "rg-menuops-prod-dr" `
  --plan "asp-menuops-prod-dr" `
  --runtime "DOTNETCORE:3.1"

# Configure Traffic Manager for failover
az network traffic-manager profile create `
  --name "tm-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --routing-method "Priority" `
  --unique-dns-name "menuops-api"

# Add primary endpoint
az network traffic-manager endpoint create `
  --name "primary" `
  --profile-name "tm-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --type "azureEndpoints" `
  --target-resource-id "/subscriptions/{subscription-id}/resourceGroups/rg-menuops-prod/providers/Microsoft.Web/sites/app-menuops-api-prod" `
  --priority 1

# Add secondary endpoint
az network traffic-manager endpoint create `
  --name "secondary" `
  --profile-name "tm-menuops-prod" `
  --resource-group "rg-menuops-prod" `
  --type "azureEndpoints" `
  --target-resource-id "/subscriptions/{subscription-id}/resourceGroups/rg-menuops-prod-dr/providers/Microsoft.Web/sites/app-menuops-api-prod-dr" `
  --priority 2
```

### Step 2: Configure Automated Backup and Restore
```powershell
# Create automation account for backup scripts
az automation account create `
  --name "aa-menuops-backup" `
  --resource-group "rg-menuops-prod" `
  --location "East US"

# Create runbook for automated backup
az automation runbook create `
  --automation-account-name "aa-menuops-backup" `
  --resource-group "rg-menuops-prod" `
  --name "BackupMenuOpsData" `
  --type "PowerShell"
```

## Cost Management and Optimization

### Step 1: Configure Cost Alerts
```powershell
# Create budget for the resource group
az consumption budget create `
  --budget-name "budget-menuops-prod" `
  --amount 500 `
  --time-grain "Monthly" `
  --time-period start-date="2024-01-01" end-date="2024-12-31" `
  --resource-group "rg-menuops-prod"
```

### Step 2: Configure Auto-shutdown for Development
```powershell
# Configure auto-shutdown for development App Service
az webapp config set `
  --name "app-menuops-api-dev" `
  --resource-group "rg-menuops-dev" `
  --always-on false

# Create Logic App for scheduled shutdown
az logic workflow create `
  --name "logic-shutdown-dev" `
  --resource-group "rg-menuops-dev" `
  --location "East US"
```

## Compliance and Governance

### Step 1: Configure Azure Policy
```powershell
# Assign built-in policy for required tags
az policy assignment create `
  --name "require-tags" `
  --policy "/providers/Microsoft.Authorization/policyDefinitions/1e30110a-5ceb-460c-a204-c1c3969c6d62" `
  --scope "/subscriptions/{subscription-id}/resourceGroups/rg-menuops-prod" `
  --params '{"tagName":{"value":"Environment"},"tagValue":{"value":"Production"}}'
```

### Step 2: Configure Azure Security Center
```powershell
# Enable Security Center standard tier
az security pricing create `
  --name "VirtualMachines" `
  --tier "Standard"

az security pricing create `
  --name "AppServices" `
  --tier "Standard"
```

This comprehensive Azure deployment guide provides enterprise-grade hosting with built-in security, monitoring, scalability, disaster recovery, and compliance features suitable for production workloads.

# MenuOps Pricing Tool - Server Setup Guide

## Overview
This guide provides step-by-step instructions for setting up and running the MenuOps Pricing Tool packages on production servers.

## Server Requirements

### Minimum Hardware Requirements
- **CPU**: 2 cores, 2.4 GHz
- **RAM**: 4 GB minimum, 8 GB recommended
- **Storage**: 10 GB free space
- **Network**: Internet connectivity for API calls

### Software Requirements

#### Option 1: Framework-Dependent Deployment
- Windows Server 2019+ or Windows 10+
- .NET Core 3.1 Runtime (ASP.NET Core Runtime)
- IIS 10+ with ASP.NET Core Hosting Bundle
- Node.js (not required for runtime, only if rebuilding)

#### Option 2: Self-Contained Deployment
- Windows Server 2019+ or Windows 10+
- IIS 10+ (optional, can run standalone)
- No .NET runtime required (included in package)

#### Option 3: Docker Deployment
- Docker Desktop (Windows) or Docker Engine (Linux)
- Docker Compose
- 4 GB RAM minimum for containers

## Pre-Installation Setup

### 1. Prepare Server Environment

#### 1.1 Install IIS (Windows)
```powershell
# Run as Administrator
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing, IIS-ASPNET45, IIS-NetFxExtensibility45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-HttpCompressionStatic, IIS-HttpCompressionDynamic, IIS-Security, IIS-RequestFiltering, IIS-BasicAuthentication, IIS-WindowsAuthentication, IIS-DigestAuthentication, IIS-ClientCertificateMappingAuthentication, IIS-IISCertificateMappingAuthentication, IIS-URLAuthorization, IIS-IPSecurity, IIS-Performance, IIS-WebServerManagementTools, IIS-ManagementConsole, IIS-IIS6ManagementCompatibility, IIS-Metabase, IIS-HostableWebCore, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing, IIS-HttpErrors, IIS-HttpRedirect, IIS-NetFxExtensibility45, IIS-ASPNET45
```

#### 1.2 Install .NET Core Runtime (Framework-Dependent Only)
1. Download ASP.NET Core 3.1 Runtime from Microsoft
2. Install the Hosting Bundle for IIS
3. Restart IIS: `iisreset`

#### 1.3 Create Application Directories
```powershell
# Create application directories
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\menuops" -Force
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\menuops-api" -Force
New-Item -ItemType Directory -Path "C:\MenuOps\logs" -Force
New-Item -ItemType Directory -Path "C:\MenuOps\config" -Force
```

## Installation Instructions

### Option 1: Framework-Dependent Deployment

#### Step 1: Extract Package
```powershell
# Extract the deployment package
Expand-Archive -Path "MenuOps-Production-v1.0.zip" -DestinationPath "C:\MenuOps\deployment"
```

#### Step 2: Deploy Frontend
```powershell
# Navigate to deployment directory
cd C:\MenuOps\deployment

# Deploy frontend using the provided script
.\deploy-frontend.ps1 -Environment "production" -TargetPath "C:\inetpub\wwwroot\menuops"
```

#### Step 3: Deploy Backend
```powershell
# Deploy backend using the provided script
.\deploy-backend.ps1 -Environment "Production" -TargetPath "C:\inetpub\wwwroot\menuops-api"
```

#### Step 4: Configure IIS

##### 4.1 Create Frontend Website
1. Open IIS Manager
2. Right-click "Sites" → "Add Website"
3. Configure:
   - **Site name**: MenuOps Frontend
   - **Physical path**: `C:\inetpub\wwwroot\menuops`
   - **Port**: 80 (or your preferred port)
   - **Host name**: your-domain.com (optional)

##### 4.2 Create Backend Application
1. Right-click "Sites" → "Add Website"
2. Configure:
   - **Site name**: MenuOps API
   - **Physical path**: `C:\inetpub\wwwroot\menuops-api`
   - **Port**: 9142
   - **Host name**: (leave blank)

##### 4.3 Configure Application Pool
1. Select the backend application pool
2. Set ".NET CLR version" to "No Managed Code"
3. Set "Managed pipeline mode" to "Integrated"

### Option 2: Self-Contained Deployment

#### Step 1: Extract and Deploy
```powershell
# Extract package
Expand-Archive -Path "MenuOps-SelfContained-v1.0.zip" -DestinationPath "C:\MenuOps"

# Deploy frontend
Copy-Item -Path "C:\MenuOps\frontend\*" -Destination "C:\inetpub\wwwroot\menuops" -Recurse -Force

# Deploy backend
Copy-Item -Path "C:\MenuOps\backend\*" -Destination "C:\MenuOps\api" -Recurse -Force
```

#### Step 2: Run Backend as Windows Service (Optional)
```powershell
# Install as Windows Service using sc command
sc create "MenuOpsAPI" binPath="C:\MenuOps\api\AMPSMiddleWare.exe" start=auto
sc description "MenuOpsAPI" "MenuOps Pricing Tool API Service"
sc start "MenuOpsAPI"
```

#### Step 3: Configure IIS for Frontend Only
Follow the same IIS configuration steps as Option 1 for the frontend, but skip the backend IIS configuration since it runs as a standalone service.

### Option 3: Docker Deployment

#### Step 1: Prepare Docker Environment
```powershell
# Ensure Docker is running
docker --version
docker-compose --version
```

#### Step 2: Extract Docker Package
```powershell
# Extract Docker package
Expand-Archive -Path "MenuOps-Docker-v1.0.zip" -DestinationPath "C:\MenuOps\docker"
cd C:\MenuOps\docker
```

#### Step 3: Configure Environment Variables
```powershell
# Create .env file for Docker Compose
@"
CFC_USERNAME=your_username
CFC_PASSWORD=your_password
CFC_BASE_URL=https://web.configurationcenter.com/ConfigurationCenter/8967
"@ | Out-File -FilePath ".env" -Encoding UTF8
```

#### Step 4: Deploy with Docker Compose
```powershell
# Build and start containers
docker-compose up -d

# Verify containers are running
docker-compose ps

# View logs
docker-compose logs -f
```

## Configuration

### 1. Backend Configuration

#### 1.1 Update appsettings.json
```json
{
  "AllowedHosts": "*",
  "CFCSettings": {
    "UserName": "YOUR_API_USERNAME",
    "Password": "YOUR_ENCRYPTED_PASSWORD",
    "BaseUrl": "https://web.configurationcenter.com/ConfigurationCenter/8967"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
```

#### 1.2 Configure CORS (if needed)
Update the CORS settings in Startup.cs configuration to match your frontend domain:
```csharp
builder.WithOrigins("https://your-domain.com")
```

### 2. Frontend Configuration

#### 2.1 Verify API Endpoint
Ensure the frontend is configured to point to the correct backend URL. This is set during the build process via the `.env-cmdrc` file.

### 3. SSL Configuration (Production)

#### 3.1 Install SSL Certificate
1. Obtain SSL certificate from your certificate authority
2. Install certificate in IIS
3. Bind HTTPS to your websites
4. Configure HTTP to HTTPS redirect

#### 3.2 Update CORS for HTTPS
Update backend CORS configuration to use HTTPS URLs.

## Verification and Testing

### 1. Health Checks

#### 1.1 Frontend Health Check
```powershell
# Test frontend accessibility
Invoke-WebRequest -Uri "http://localhost/health" -UseBasicParsing
```

#### 1.2 Backend Health Check
```powershell
# Test backend API
Invoke-WebRequest -Uri "http://localhost:9142/health" -UseBasicParsing
```

### 2. Application Testing

#### 2.1 Access Frontend
1. Open web browser
2. Navigate to `http://your-server-ip` or `https://your-domain.com`
3. Verify the application loads correctly

#### 2.2 Test API Connectivity
1. Use browser developer tools
2. Check Network tab for successful API calls
3. Verify no CORS errors in console

## Monitoring and Maintenance

### 1. Log Locations
- **Frontend Logs**: IIS logs in `C:\inetpub\logs\LogFiles`
- **Backend Logs**: Application logs in `C:\MenuOps\logs` or configured log directory
- **Docker Logs**: Use `docker-compose logs` command

### 2. Performance Monitoring
- Monitor CPU and memory usage
- Check disk space regularly
- Monitor network connectivity to external APIs

### 3. Regular Maintenance
- Apply Windows updates
- Update SSL certificates before expiration
- Monitor application logs for errors
- Backup configuration files

## Troubleshooting

### Common Issues

#### 1. Application Won't Start
- Verify .NET Core runtime is installed (framework-dependent)
- Check application pool configuration
- Review Windows Event Log for errors

#### 2. CORS Errors
- Verify CORS configuration in backend
- Check frontend is using correct API URL
- Ensure protocol (HTTP/HTTPS) matches

#### 3. API Connection Issues
- Verify backend service is running
- Check firewall settings
- Test network connectivity to external APIs

### Support
For additional support, check the application logs and refer to the troubleshooting section in the main deployment guide.

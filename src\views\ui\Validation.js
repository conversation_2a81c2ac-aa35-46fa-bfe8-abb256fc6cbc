import React, { useRef, useState } from "react";
import * as XLSX from "xlsx";
import { <PERSON><PERSON>, Bad<PERSON>, <PERSON>ton, Card, CardBody, CardTitle, CardText, Row, Col, UncontrolledDropdown, DropdownToggle, DropdownMenu, DropdownItem, Dropdown, ButtonGroup } from "reactstrap";
import { Link } from 'react-router-dom'
import Globals from '../../Globals';

import { v4 as uuid4 } from 'uuid';
import { log } from './Logger';
import LoadingAnim from "../../assets/images/logos/loading2.gif";
import jsPDF from "jspdf";
import "jspdf-autotable";
import { format } from "date-fns";
import HeaderConfigs from './UiConfig.js';

export default function Validation() {

    const [isloadingDivVisible, setloadingDivVisibility] = useState(false)

    const getStorePriceChangeSchedule = async () => {


        setloadingDivVisibility(!isloadingDivVisible);

        //Globals.storeUniqueID = "3c43cd17-49cc-43ca-8f71-a15c7886cd25";

        const startDate = new Date(Globals.ActivationDate);
        var jsonPriceChangeScheduleTxt = '{ "Header" ';
        jsonPriceChangeScheduleTxt += ":";
        jsonPriceChangeScheduleTxt += "{";
        jsonPriceChangeScheduleTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
        jsonPriceChangeScheduleTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
        jsonPriceChangeScheduleTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
        jsonPriceChangeScheduleTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID;
        jsonPriceChangeScheduleTxt += " },";
        jsonPriceChangeScheduleTxt += '"EffectiveDate"' + " : " + '"' + new Date(startDate).toLocaleString() + '"' + " , ";
        jsonPriceChangeScheduleTxt += '"StoreUniqueID"' + " : " + '"' + Globals.storeUniqueID + '"'
        jsonPriceChangeScheduleTxt += " }";

        let response = await fetch(Globals.BaseURL + '/CollectPriceChangesForStore', {
            method: 'POST',
            body:

                jsonPriceChangeScheduleTxt

            ,
            headers: {
                'Content-type': 'application/json; charset=UTF-8',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip,deflate',

            },
        });

        let data = await response.json();

        //console.log(data.List);


        Globals.StorePriceChanges = JSON.stringify(data.List);
        //console.log("StorePriceChanges");
        //console.log(Globals.StorePriceChanges);

        var myObject = JSON.parse(Globals.StorePriceChanges);
        const arrMenuItems = [];
        const arrTemplateItems = [];
        var blnMatchFound = false;
        console.log(myObject.length);

        var myTemplateObject = JSON.parse(Globals.TemplateData);
        for (let i = 0; i < myTemplateObject.length; i++) {

            arrTemplateItems.push({ name: myTemplateObject[i].ItemName, value: parseFloat(myTemplateObject[i].AlaCartePrice) });
        }


        for (let i = 0; i < myObject.length; i++) {


            if (new Date(Globals.ActivationDate).toLocaleString() == new Date(myObject[i].StartDate).toLocaleString()) {

                blnMatchFound = true;

                var menuItems = myObject[i].MenuItemAssignments;

                //console.log(menuItems.length);

                for (let j = 0; j < menuItems.length; j++) {

                    //console.log(menuItems[j].Price);
                    //console.log(menuItems[j].MenuItem.MenuItemName);
                    arrMenuItems.push({ name: menuItems[j].MenuItem.MenuItemName, value: menuItems[j].Price });
                }





            }

        }

        //console.log(arrMenuItems);
        //console.log(arrTemplateItems);

        if (blnMatchFound) {
            const areEqual = arrTemplateItems.every(pair1 => arrMenuItems.find(pair2 => pair2.name === pair1.name && pair2.value === pair1.value));

            if (!areEqual) {
                //Validation Failed	
            }
        } else {

            //Validation Failed
        }
        //console.log(areEqual);




        setloadingDivVisibility(false);

    }

    const runValidation = async () => {

        Globals.GlobalPricingRecordID = "f04b0c4e-84cf-41a7-9a1a-c1f517fae942";
        const startDate = new Date(Globals.ActivationDate);
        var jsonPriceChangeTxt = '{ "Header" ';
        jsonPriceChangeTxt += ":";
        jsonPriceChangeTxt += "{";
        jsonPriceChangeTxt += ' "ClientApplication" ' + " : " + HeaderConfigs.ClientApplication + " , ";
        jsonPriceChangeTxt += '"ClientVersion"' + " : " + HeaderConfigs.ClientVersion + " , ";
        jsonPriceChangeTxt += '"ApiVersion"' + " : " + HeaderConfigs.ApiVersion + " , ";
        jsonPriceChangeTxt += '"MessageID"' + " : " + HeaderConfigs.MessageID;
        jsonPriceChangeTxt += " },";
        jsonPriceChangeTxt += '"PriceChangeUniqueID"' + " : " + '"' + Globals.GlobalPricingRecordID + '"'
        jsonPriceChangeTxt += " }";

        let response = await fetch('http://localhost:9142/ValidatePriceChange', {
            method: 'POST',
            body:

                jsonPriceChangeTxt

            ,
            headers: {
                'Content-type': 'application/json; charset=UTF-8',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip,deflate',

            },
        });
        console.log(jsonPriceChangeTxt);
        let data = await response.json();

        Globals.StorePriceChangeValidation = JSON.stringify(data.list);
        console.log("StorePriceChangeValidation");
        console.log(Globals.StorePriceChangeValidation);

        var myObject = JSON.parse(Globals.StorePriceChangeValidation);






    };


    return (
        <div>
            <h1>
                <Badge color="secondary">Run Validations</Badge>
            </h1>

            <Card>
                <CardTitle tag="h6" className="border-bottom p-3 mb-0">
                    <b>Run validation on the imported template</b>
                </CardTitle>
                <CardBody className="">
                    <div className="mt-3">

                        <Alert color="success">
                            <b>Template File Name  :: </b> {Globals.TemplateFileName}

                        </Alert>

                    </div>
                </CardBody>
            </Card>

            <ButtonGroup>

                <Button onClick={() => { getStorePriceChangeSchedule(); }} color="secondary"><b>Run Validation</b></Button>

            </ButtonGroup>
            <div id="loadingDiv" style={{ display: (isloadingDivVisible) ? 'block' : 'none' }}>
                <br />
                <br />
                <Alert color="danger"> Please wait while we run validations</Alert>
                <br />
                <img src={LoadingAnim} style={{ "position": "relative", top: -220 }} />
            </div>

        </div>
    );
};



﻿using AMPSMiddleWare.Models.Promotion;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChangePromotionQuickCombo
    {
        public PromotionReference Promotion { get; set; }
        public float? RegularPrice { get; set; }
        public float? Upsell1Price { get; set; }
        public float? Upsell2Price { get; set; }
        public List<PriceChangePromotionQuickComboComponent> Components { get; set; }
    }
}

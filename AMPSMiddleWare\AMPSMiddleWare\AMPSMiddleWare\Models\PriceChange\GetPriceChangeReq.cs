﻿using AMPSMiddleWare.Models._BaseModels.BaseRequest;
using AMPSMiddleWare.Settings;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class GetPriceChangeReq : PriceChangeReference
    {
        public GetPriceChangeReq(CFCSettings settings)
        {
            this.Header = new RequestHeader(settings);
        }

        public RequestHeader Header { get; set; }
    }
}

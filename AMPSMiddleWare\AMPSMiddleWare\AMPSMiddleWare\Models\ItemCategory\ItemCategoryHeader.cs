﻿using AMPSMiddleWare.Enums;
using AMPSMiddleWare.Models._BaseModels.BaseHeader;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.ItemCategory
{
    public class ItemCategoryHeader : HeaderBase
    {
        public Guid ItemCategoryUniqueID { get; set; }
        public string ItemCategoryID { get; set; }
        public ItemCategoryType Type { get; set; }
    }
}

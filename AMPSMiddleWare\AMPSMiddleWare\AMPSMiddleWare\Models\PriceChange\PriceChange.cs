﻿using AMPSMiddleWare.Models.Owner;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChange
    {
        public Guid PriceChangeUniqueID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public OwnerReference Owner { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? POSStartDate { get; set; }
        public DateTime? POSEndDate { get; set; }
        public string Name { get; set; }
        public int POSNumber { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<PriceChangeMenuItem> MenuItemAssignments { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<PriceChangePriceLevel> PriceLevelAssignments { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<PriceChangePromoAssignmentUnion> PromoAssignments { get; set; }
    }
}

# MenuOps Pricing Tool - Package Creation Script
# This script automates the packaging process for deployment

param(
    [Parameter(Mandatory=$false)]
    [string]$Version = "1.0",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = ".\packages",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipClean = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateDockerPackage = $true
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Host "=== MenuOps Pricing Tool - Package Creation ===" -ForegroundColor Green
Write-Host "Version: $Version" -ForegroundColor Yellow
Write-Host "Output Path: $OutputPath" -ForegroundColor Yellow
Write-Host "Project Root: $ProjectRoot" -ForegroundColor Yellow

try {
    # Change to project root
    Set-Location $ProjectRoot
    
    # Step 1: Clean previous builds
    if (-not $SkipClean) {
        Write-Host "Cleaning previous builds..." -ForegroundColor Cyan
        Remove-Item -Path "build" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\bin" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\obj" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\publish" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item -Path $OutputPath -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Step 2: Create package directory structure
    Write-Host "Creating package directory structure..." -ForegroundColor Cyan
    New-Item -ItemType Directory -Path "$OutputPath\frontend\production" -Force | Out-Null
    New-Item -ItemType Directory -Path "$OutputPath\frontend\qa" -Force | Out-Null
    New-Item -ItemType Directory -Path "$OutputPath\frontend\development" -Force | Out-Null
    New-Item -ItemType Directory -Path "$OutputPath\backend\production" -Force | Out-Null
    New-Item -ItemType Directory -Path "$OutputPath\backend\qa" -Force | Out-Null
    New-Item -ItemType Directory -Path "$OutputPath\backend\self-contained" -Force | Out-Null
    New-Item -ItemType Directory -Path "$OutputPath\config" -Force | Out-Null
    New-Item -ItemType Directory -Path "$OutputPath\deployment" -Force | Out-Null
    
    # Step 3: Install frontend dependencies
    Write-Host "Installing frontend dependencies..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        throw "npm install failed"
    }
    
    # Step 4: Build frontend for all environments
    Write-Host "Building frontend for production..." -ForegroundColor Cyan
    npm run build-prod
    if ($LASTEXITCODE -ne 0) {
        throw "Frontend production build failed"
    }
    Copy-Item -Path "build\*" -Destination "$OutputPath\frontend\production\" -Recurse -Force
    
    Write-Host "Building frontend for QA..." -ForegroundColor Cyan
    npm run build-qa
    if ($LASTEXITCODE -ne 0) {
        throw "Frontend QA build failed"
    }
    Copy-Item -Path "build\*" -Destination "$OutputPath\frontend\qa\" -Recurse -Force
    
    Write-Host "Building frontend for development..." -ForegroundColor Cyan
    npm run build
    if ($LASTEXITCODE -ne 0) {
        throw "Frontend development build failed"
    }
    Copy-Item -Path "build\*" -Destination "$OutputPath\frontend\development\" -Recurse -Force
    
    # Step 5: Build backend
    $BackendPath = "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare"
    Set-Location $BackendPath
    
    Write-Host "Restoring backend dependencies..." -ForegroundColor Cyan
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        throw "dotnet restore failed"
    }
    
    Write-Host "Publishing backend for production..." -ForegroundColor Cyan
    dotnet publish -c Release -o "..\..\..\$OutputPath\backend\production" --self-contained false
    if ($LASTEXITCODE -ne 0) {
        throw "Backend production publish failed"
    }
    
    Write-Host "Publishing backend for QA..." -ForegroundColor Cyan
    dotnet publish -c Release -o "..\..\..\$OutputPath\backend\qa" --self-contained false
    if ($LASTEXITCODE -ne 0) {
        throw "Backend QA publish failed"
    }
    
    Write-Host "Publishing self-contained backend..." -ForegroundColor Cyan
    dotnet publish -c Release -o "..\..\..\$OutputPath\backend\self-contained" --self-contained true -r win-x64
    if ($LASTEXITCODE -ne 0) {
        throw "Backend self-contained publish failed"
    }
    
    # Return to project root
    Set-Location $ProjectRoot
    
    # Step 6: Copy configuration templates
    Write-Host "Copying configuration templates..." -ForegroundColor Cyan
    Copy-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\appsettings.json" -Destination "$OutputPath\config\appsettings.template.json"
    Copy-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\log4net.config" -Destination "$OutputPath\config\log4net.config" -ErrorAction SilentlyContinue
    Copy-Item -Path ".env-cmdrc" -Destination "$OutputPath\config\.env-cmdrc.template"
    
    # Step 7: Copy deployment scripts and configurations
    Write-Host "Copying deployment assets..." -ForegroundColor Cyan
    Copy-Item -Path "deployment\deploy-frontend.ps1" -Destination "$OutputPath\deployment\"
    Copy-Item -Path "deployment\deploy-backend.ps1" -Destination "$OutputPath\deployment\"
    Copy-Item -Path "deployment\docker-compose.yml" -Destination "$OutputPath\deployment\"
    Copy-Item -Path "deployment\Dockerfile.*" -Destination "$OutputPath\deployment\"
    Copy-Item -Path "deployment\nginx.conf" -Destination "$OutputPath\deployment\"
    Copy-Item -Path "deployment\web.config" -Destination "$OutputPath\deployment\" -ErrorAction SilentlyContinue
    
    # Step 8: Copy documentation
    Write-Host "Copying documentation..." -ForegroundColor Cyan
    Copy-Item -Path "deployment\README.md" -Destination "$OutputPath\DEPLOYMENT_GUIDE.md"
    Copy-Item -Path "deployment\PACKAGING_GUIDE.md" -Destination "$OutputPath\PACKAGING_GUIDE.md"
    Copy-Item -Path "deployment\SERVER_SETUP_GUIDE.md" -Destination "$OutputPath\SERVER_SETUP_GUIDE.md"
    
    # Step 9: Create distribution archives
    Write-Host "Creating distribution archives..." -ForegroundColor Cyan
    
    # Production package
    $ProductionFiles = @(
        "$OutputPath\frontend\production\*"
        "$OutputPath\backend\production\*"
        "$OutputPath\config\*"
        "$OutputPath\deployment\*"
        "$OutputPath\*.md"
    )
    Compress-Archive -Path $ProductionFiles -DestinationPath "MenuOps-Production-v$Version.zip" -Force
    
    # QA package
    $QAFiles = @(
        "$OutputPath\frontend\qa\*"
        "$OutputPath\backend\qa\*"
        "$OutputPath\config\*"
        "$OutputPath\deployment\*"
        "$OutputPath\*.md"
    )
    Compress-Archive -Path $QAFiles -DestinationPath "MenuOps-QA-v$Version.zip" -Force
    
    # Self-contained package
    $SelfContainedFiles = @(
        "$OutputPath\frontend\production\*"
        "$OutputPath\backend\self-contained\*"
        "$OutputPath\config\*"
        "$OutputPath\deployment\*"
        "$OutputPath\*.md"
    )
    Compress-Archive -Path $SelfContainedFiles -DestinationPath "MenuOps-SelfContained-v$Version.zip" -Force
    
    # Docker package
    if ($CreateDockerPackage) {
        $DockerPackageDir = "docker-package"
        New-Item -ItemType Directory -Path $DockerPackageDir -Force | Out-Null
        Copy-Item -Path "$OutputPath\deployment\docker-compose.yml" -Destination "$DockerPackageDir\"
        Copy-Item -Path "$OutputPath\deployment\Dockerfile.*" -Destination "$DockerPackageDir\"
        Copy-Item -Path "$OutputPath\deployment\nginx.conf" -Destination "$DockerPackageDir\"
        Copy-Item -Path "$OutputPath\config\*" -Destination "$DockerPackageDir\"
        Copy-Item -Path "$OutputPath\DEPLOYMENT_GUIDE.md" -Destination "$DockerPackageDir\"
        
        Compress-Archive -Path "$DockerPackageDir\*" -DestinationPath "MenuOps-Docker-v$Version.zip" -Force
        Remove-Item -Path $DockerPackageDir -Recurse -Force
    }
    
    # Step 10: Validation
    Write-Host "Validating packages..." -ForegroundColor Cyan
    $packages = @(
        "MenuOps-Production-v$Version.zip"
        "MenuOps-QA-v$Version.zip"
        "MenuOps-SelfContained-v$Version.zip"
    )
    
    if ($CreateDockerPackage) {
        $packages += "MenuOps-Docker-v$Version.zip"
    }
    
    foreach ($package in $packages) {
        if (Test-Path $package) {
            $size = (Get-Item $package).Length / 1MB
            Write-Host "✓ $package ($([math]::Round($size, 2)) MB)" -ForegroundColor Green
        } else {
            Write-Host "✗ $package - NOT FOUND" -ForegroundColor Red
        }
    }
    
    Write-Host "=== Package Creation Completed Successfully! ===" -ForegroundColor Green
    Write-Host "Packages created:" -ForegroundColor Yellow
    foreach ($package in $packages) {
        if (Test-Path $package) {
            Write-Host "  - $package" -ForegroundColor Yellow
        }
    }
    
} catch {
    Write-Host "=== Package Creation Failed! ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Return to script directory
    Set-Location $ScriptDir
}

Write-Host "Package creation script completed." -ForegroundColor Green

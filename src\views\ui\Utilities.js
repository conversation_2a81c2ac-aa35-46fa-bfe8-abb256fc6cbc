import React, { useState } from "react";
import * as XLSX from "xlsx";
import {
    Col,
    Card,
    CardBody,
    CardTitle, Badge, Button, FormGroup, Input, Label, CardText, Row, Form
} from "reactstrap";


const Utilities = () => {

    const [pricelevelstate, setState] = useState(true);

    const generateTemplate = () => {

        const templateHeaders = [
            { SiteName: "", StartDate: "", PosNumber: "", ItemName: "", AlaCartePrice: "" }
        ];

        const wb = XLSX.utils.book_new();

        const ws_ItemPrice = XLSX.utils.json_to_sheet(templateHeaders);
        ws_ItemPrice['!cols'] = [{ wch: 10 }, { wch: 10 }, { wch: 15 }, { wch: 30 }, { wch: 15 }];
        XLSX.utils.book_append_sheet(wb, ws_ItemPrice, 'ItemPricing');

        if (pricelevelstate) {
            const ws_PriceLevel = XLSX.utils.json_to_sheet(templateHeaders);
            ws_PriceLevel['!cols'] = [{ wch: 10 }, { wch: 10 }, { wch: 15 }, { wch: 30 }, { wch: 15 }];
            XLSX.utils.book_append_sheet(wb, ws_PriceLevel, 'PriceLevels');
        }

        XLSX.writeFile(wb, 'MPT_TemplateData.xlsx');


    };

    return (
        <div>
            <h1>
                <Badge color="secondary">Utilities</Badge>
            </h1>
            <Row>
                <Col md="8" lg="40">
                    <Card>
                        <CardTitle tag="h5" className="border-bottom p-3 mb-0">
                            <b>Download the Import Template</b>
                        </CardTitle>
                        <CardBody>
                            <CardText>
                                This template will only have required headers, no data.
                            </CardText>
                            <Form>
                                <FormGroup switch>
                                    <Input
                                        type="switch"
                                        role="switch"
                                        readOnly
                                        checked={pricelevelstate}
                                        onClick={() => {
                                            setState(!pricelevelstate);
                                        }}
                                    />
                                    <Label check>Include PriceLevel Sheet</Label>
                                </FormGroup>
                            </Form>
                            <Button onClick={() => { generateTemplate(); }} color="secondary"><b>Download</b></Button>&nbsp;
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default Utilities;

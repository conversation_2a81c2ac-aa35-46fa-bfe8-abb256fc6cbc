import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { <PERSON>ge, <PERSON>ton, Card, CardBody, Row, Col, ButtonGroup } from "reactstrap";
import { Link } from 'react-router-dom'
import { IdleTimerProvider, useIdleTimerContext } from 'react-idle-timer/dist/index.legacy.esm.js'
import Globals from '../../Globals';
import { log } from './Logger';
import { resetSessionTimeout } from './SessionManager';
import './Environment.scss';
import HeaderConfigs from './UiConfig';

const burl = process.env.REACT_APP_SERVER_URL
Globals.BaseURL = burl

const Main = styled("div")`
  font-family: sans-serif;
   
  height: 100vh;
`;

/*
const DropDownContainer = styled("div")`
  width: 20.5em;
  margin: 0 auto;
`;

const DropDownHeader = styled("div")`
  margin-bottom: 0.8em;
  padding: 0.4em 2em 0.4em 1em;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
  font-weight: 500;
  font-size: 1.3rem;
  color: #3faffa;
  background: #ffffff;
`;
const ListItem = styled("li")`
  list-style: none;
  margin-bottom: 0.8em;
`;

const DropDownListContainer = styled("div")``; */

const DropDownList = styled("ul")`
  padding: 0;
  margin: 0;
  padding-left: 1em;
  background: #ffffff;
  border: 2px solid #e5e5e5;
  box-sizing: border-box;
  color: #3faffa;
  font-size: 1.3rem;
  font-weight: 500;
  &:first-child {
    padding-top: 0.8em;
  }
`;

const selectStype = {
  padding: '0',
  width: '150px'
};


var url = '/environment';  //: '/import';

export default function App() {
  const [customerConfigs, setCustomerConfigs] = useState([]);
  const [selectedCustConfig, setSelectedCustConfig] = useState('');

  const [customerEnv, setCustomerEnv] = useState([]);
  const [selectedCustEnv, setSelectedCustEnv] = useState('');

  const idleTimer = useIdleTimerContext()

  const onIdle = () => {
    console.log("user is idle");
    navigate('/login');
  }



  const navigate = useNavigate();
  useEffect(() => {
    const element = document.getElementById('LogOut');
    element.style = { display: 'block' };

    fetch(Globals.BaseURL + '/api/CustomerConfig')
      .then(response => response.json())
      .then(data => setCustomerConfigs(data))
      .catch(error => console.error('Error fetching CustomerConfic options:', error));

    //console.log("1 St Effect is called:::" + url);

    //Reset session timeout when this page is loaded.
    //resetSessionTimeout(logout, initialTimeout);
    if (!Globals.isUserLoggedIn) {
      navigate('/login');
    }
  }, []);


  useEffect(() => {
    // Update the document title using the browser API

    if (Globals.SelectedCustomer != null && Globals.SelectedEnvironment != null)
      url = '/activation';

    //console.log("2nd Effect is called:::" + url);
    //console.log("Selected Environment URL is: " + Globals.SelectedEnvironmentURL)
  });

  const handleSelectCustomer = (event) => {
    setSelectedCustConfig(event.target.value);
    setSelectedCustEnv('');

    if (event.target.value === "select") {
      setCustomerEnv([]);
      Globals.SelectedCustomer = ''
    }
    else {
      setCustomerEnv(customerConfigs.find(t => t.customerName === event.target.value).environments);
      Globals.SelectedCustomer = event.target.value;

    }
    Globals.SelectedEnvironment = '';
    Globals.SelectedEnvironmentURL = '';

  };


  const handleSelectEnvironment = (event) => {
    if (event.target.value === "select") {
      setSelectedCustEnv('');
      Globals.SelectedEnvironment = '';
    }
    else {
      setSelectedCustEnv(event.target.value);
      Globals.SelectedEnvironment = event.target.value;
    }
    Globals.SelectedEnvironmentURL = customerEnv.find(t => t.envName === event.target.value).envURL;

  };


  const btnContinue = () => {

    const logresult1 = log("User selected customer : " + Globals.SelectedCustomer + ".");
    const logresult2 = log("User selected environment : " + Globals.SelectedEnvironment + ".");
    const logresult3 = log("EnvironmentURL : " + Globals.SelectedEnvironmentURL + ".");


    navigate(url);

  }

  return (



    <Main>
      <h1>
        <Badge color="secondary">Choose Your CFC Customer and Environment</Badge>
      </h1>

      <IdleTimerProvider
        timeout={10000 * 60}
        onIdle={onIdle}

      />

      <Row>
        <Col xs="12" md="6">

          <Card>

            <CardBody className="">

              <Badge color="secondary">CFC Customer</Badge>
              <DropDownList>
                <select value={selectedCustConfig} onChange={handleSelectCustomer} style={selectStype}>
                  <option key='0' value="select" >Select</option>
                  {customerConfigs.map(option => (
                    <option key={option.id} value={option.customerName}>
                      {option.customerName}
                    </option>
                  ))}

                </select>
              </DropDownList>

            </CardBody>
          </Card>

        </Col>

        <Col xs="18" md="6">
          <Card>
            <CardBody className="">
              <Badge color="secondary">Customer Environment</Badge>
              <DropDownList>
                <select value={selectedCustEnv} onChange={handleSelectEnvironment} style={selectStype} >
                  <option key='0' value="select" >Select</option>
                  {customerEnv.map(option => (
                    <option key={option.envID} value={option.envName}>
                      {option.envName}
                    </option>
                  ))}

                </select>
              </DropDownList>
            </CardBody>
          </Card>

        </Col>

      </Row>

      <CardBody className="">

        <ButtonGroup>
          <Button onClick={() => { btnContinue(); }} disabled={selectedCustConfig === "" || selectedCustConfig === null || selectedCustEnv === "" || selectedCustEnv === null} color="secondary"><b>Continue</b></Button>
        </ButtonGroup>

      </CardBody>

    </Main>
  );
}
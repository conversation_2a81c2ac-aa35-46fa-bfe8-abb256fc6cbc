﻿using AMPSMiddleWare.Models._BaseModels.BaseRequest;
using Microsoft.Extensions.Options;
using AMPSMiddleWare.Settings;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.MenuItem
{
    public class UpdateMenuItemsReq
    {
        public UpdateMenuItemsReq(CFCSettings settings)
        {
            this.Header = new RequestHeader(settings);
        }

        public RequestHeader Header { get; set; }
        public List<MenuItemMinimalData> Batch { get; set; }
    }
}

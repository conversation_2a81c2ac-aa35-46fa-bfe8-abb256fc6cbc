import { Button, Nav, NavItem } from "reactstrap";
import { Link, useLocation } from "react-router-dom";

const navigation = [
  {
    title: "CFC Environment",
    href: "/environment",
    icon: "bi bi-card-text",
  },
  {
    title: "Activation Schedule",
    href: "/activation",
    icon: "bi bi-calendar-date-fill",
  }, 
  {
    title: "Import Template",
    href: "/import",
    icon: "bi bi-card-text",
  },  
    
  {
    title: "Level Selector",
    href: "/selector",
    icon: "bi bi-sort-up",
  },
  
 /*  
  {
    title: "Store",
    href: "/store",
    icon: "bi bi-house",
  },
 */
  {
    title: "Apply Changes",
    href: "/update",
    icon: "bi bi-patch-check",
  },
  /*
  {
    title: "Post Validations",
    href: "/validation",
    icon: "bi bi-link",
  },
  */
  {
    title: "Reports",
    href: "/report",
    icon: "bi bi-file-bar-graph-fill",
  },
  {
    title: "History",
    href: "/history",
    icon: "bi bi-clock-history",
  },
  {
    title: "FAQs",
    href: "/grid",
    icon: "bi bi-bell-fill",
  },
 
  {
    title: "Export Pricing",
    href: "/export",
    icon: "bi bi-card-text",
  },
  {
    title: "Utilities",
    href: "/utilities",
    icon: "bi bi-tools",    
  },
  
];

const Sidebar = () => {
  const showMobilemenu = () => {
    document.getElementById("sidebarArea").classList.toggle("showSidebar");
  };
  let location = useLocation();

  return (
    <div className="bg-dark">
      <div className="d-flex">
        <Button
          color="white"
          className="ms-auto text-white d-lg-none"
          onClick={() => showMobilemenu()}
        >
          <i className="bi bi-x"></i>
        </Button>
      </div>
	  
      <div className="p-3 mt-2">
        <Nav vertical className="sidebarNav">
          {navigation.map((navi, index) => (
            <NavItem key={index} className="sidenav-bg">
              <Link
                to={navi.href}
                className={
                  location.pathname === navi.href
                    ? "active nav-link py-3"
                    : "nav-link py-3"
                }
              >
                <i className={navi.icon}></i>
                <span className="ms-3 d-inline-block">{navi.title}</span>
              </Link>
            </NavItem>
          ))}
		
           
		 
        </Nav>
		 
      </div>
    </div>
  );
};

export default Sidebar;

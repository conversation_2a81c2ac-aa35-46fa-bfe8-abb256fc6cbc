{"AllowedHosts": "*", "CFCUrls": {"URLS": [{"Name": "<PERSON><PERSON>", "Env": {"Prod": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-Prod-<PERSON><PERSON>", "PreProd": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-PreP<PERSON>-<PERSON><PERSON>", "Test": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-test-<PERSON><PERSON>"}}, {"Name": "BurgerKing", "Env": {"Prod": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-Prod-BurgerKing", "PreProd": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-PreProd-BurgerKing", "Test": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-test-BurgerKing"}}, {"Name": "FireHouse", "Env": {"Prod": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-Prod-FireHouse", "PreProd": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-PreProd-FireHouse", "Test": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-test-FireHouse"}}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Env": {"Prod": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-Prod-<PERSON><PERSON>", "PreProd": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-PreProd-Tim<PERSON>ons", "Test": "https://web.configurationcenter.com/ConfigurationCenter/amps-lab-Test-Tim<PERSON><PERSON>"}}]}, "CFCSettings": {"UserName": "APIUser", "Password": "I6xu1KfKaSSK5c3z2hekoA==", "BaseUrl": "https://web.configurationcenter.com/ConfigurationCenter/8967", "ClientApplication": "AMPS", "ClientVersion": "*******", "ApiVersion": "*********", "MessageID": "e22b1623-7962-4daf-a2a2-1b832fb6a59f"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}
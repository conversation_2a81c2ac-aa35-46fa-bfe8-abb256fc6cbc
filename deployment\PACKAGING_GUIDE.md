# MenuOps Pricing Tool - Packaging Guide

## Overview
This guide provides step-by-step instructions for creating deployment packages without including the source code, suitable for distribution to production servers.

## Prerequisites

### Development Machine
- Node.js 14+ and npm
- .NET Core 3.1 SDK
- PowerShell 5.1+
- 7-Zip or WinRAR (for creating archives)

## Step 1: Prepare the Build Environment

### 1.1 Clean the Development Environment
```powershell
# Navigate to project root
cd C:\Projects\MenuOpsPricingTool

# Clean previous builds
Remove-Item -Path "build" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\bin" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\obj" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\publish" -Recurse -Force -ErrorAction SilentlyContinue
```

### 1.2 Install Dependencies
```powershell
# Install frontend dependencies
npm install

# Restore backend dependencies
cd AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare
dotnet restore
cd ..\..\..
```

## Step 2: Build Frontend for All Environments

### 2.1 Build Production Version
```powershell
npm run build-prod
```

### 2.2 Build QA Version
```powershell
npm run build-qa
```

### 2.3 Build Development Version
```powershell
npm run build
```

### 2.4 Create Frontend Package Structure
```powershell
# Create package directories
New-Item -ItemType Directory -Path "packages\frontend\production" -Force
New-Item -ItemType Directory -Path "packages\frontend\qa" -Force
New-Item -ItemType Directory -Path "packages\frontend\development" -Force

# Copy build outputs
Copy-Item -Path "build\*" -Destination "packages\frontend\production\" -Recurse -Force

# Rebuild for QA and copy
npm run build-qa
Copy-Item -Path "build\*" -Destination "packages\frontend\qa\" -Recurse -Force

# Rebuild for development and copy
npm run build
Copy-Item -Path "build\*" -Destination "packages\frontend\development\" -Recurse -Force
```

## Step 3: Build Backend for All Environments

### 3.1 Build and Publish Backend
```powershell
cd AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare

# Build for Production
dotnet publish -c Release -o ..\..\..\packages\backend\production --self-contained false

# Build for QA (if different configuration needed)
dotnet publish -c Release -o ..\..\..\packages\backend\qa --self-contained false

# Build self-contained version (includes .NET runtime)
dotnet publish -c Release -o ..\..\..\packages\backend\self-contained --self-contained true -r win-x64

cd ..\..\..
```

## Step 4: Create Configuration Templates

### 4.1 Create Configuration Directory
```powershell
New-Item -ItemType Directory -Path "packages\config" -Force
```

### 4.2 Copy Configuration Files
```powershell
# Copy backend configuration templates
Copy-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\appsettings.json" -Destination "packages\config\appsettings.template.json"
Copy-Item -Path "AMPSMiddleWare\AMPSMiddleWare\AMPSMiddleWare\log4net.config" -Destination "packages\config\log4net.config" -ErrorAction SilentlyContinue

# Copy frontend environment configuration
Copy-Item -Path ".env-cmdrc" -Destination "packages\config\.env-cmdrc.template"
```

## Step 5: Package Deployment Scripts and Documentation

### 5.1 Copy Deployment Assets
```powershell
# Copy deployment scripts and configurations
Copy-Item -Path "deployment\*" -Destination "packages\deployment\" -Recurse -Force

# Remove source-specific files that aren't needed in package
Remove-Item -Path "packages\deployment\*.md" -Force -ErrorAction SilentlyContinue
```

### 5.2 Create Package Documentation
```powershell
# Copy essential documentation
Copy-Item -Path "deployment\README.md" -Destination "packages\DEPLOYMENT_GUIDE.md"
Copy-Item -Path "deployment\PACKAGING_GUIDE.md" -Destination "packages\PACKAGING_GUIDE.md"
```

## Step 6: Create Final Package Structure

### 6.1 Organize Package Directory
Your final package structure should look like this:
```
packages/
├── frontend/
│   ├── production/          # Built React app for production
│   ├── qa/                  # Built React app for QA
│   └── development/         # Built React app for development
├── backend/
│   ├── production/          # Published .NET Core app (framework-dependent)
│   ├── qa/                  # Published .NET Core app for QA
│   └── self-contained/      # Self-contained .NET Core app
├── config/
│   ├── appsettings.template.json
│   ├── log4net.config
│   └── .env-cmdrc.template
├── deployment/
│   ├── deploy-frontend.ps1
│   ├── deploy-backend.ps1
│   ├── docker-compose.yml
│   ├── Dockerfile.frontend
│   ├── Dockerfile.backend
│   ├── nginx.conf
│   └── web.config
├── DEPLOYMENT_GUIDE.md
├── PACKAGING_GUIDE.md
└── SERVER_SETUP_GUIDE.md
```

## Step 7: Create Distribution Archives

### 7.1 Create Environment-Specific Packages
```powershell
# Create production package
Compress-Archive -Path "packages\frontend\production\*", "packages\backend\production\*", "packages\config\*", "packages\deployment\*", "packages\*.md" -DestinationPath "MenuOps-Production-v1.0.zip"

# Create QA package
Compress-Archive -Path "packages\frontend\qa\*", "packages\backend\qa\*", "packages\config\*", "packages\deployment\*", "packages\*.md" -DestinationPath "MenuOps-QA-v1.0.zip"

# Create self-contained package (no .NET runtime required on server)
Compress-Archive -Path "packages\frontend\production\*", "packages\backend\self-contained\*", "packages\config\*", "packages\deployment\*", "packages\*.md" -DestinationPath "MenuOps-SelfContained-v1.0.zip"
```

### 7.2 Create Docker Package
```powershell
# Create Docker-specific package
New-Item -ItemType Directory -Path "docker-package" -Force
Copy-Item -Path "packages\deployment\docker-compose.yml" -Destination "docker-package\"
Copy-Item -Path "packages\deployment\Dockerfile.*" -Destination "docker-package\"
Copy-Item -Path "packages\deployment\nginx.conf" -Destination "docker-package\"
Copy-Item -Path "packages\config\*" -Destination "docker-package\"
Copy-Item -Path "packages\DEPLOYMENT_GUIDE.md" -Destination "docker-package\"

Compress-Archive -Path "docker-package\*" -DestinationPath "MenuOps-Docker-v1.0.zip"
```

## Step 8: Validation

### 8.1 Verify Package Contents
```powershell
# List contents of each package
Get-ChildItem -Path "MenuOps-Production-v1.0.zip" | Format-Table
Get-ChildItem -Path "MenuOps-QA-v1.0.zip" | Format-Table
Get-ChildItem -Path "MenuOps-SelfContained-v1.0.zip" | Format-Table
Get-ChildItem -Path "MenuOps-Docker-v1.0.zip" | Format-Table
```

### 8.2 Test Package Extraction
```powershell
# Test extraction to verify package integrity
New-Item -ItemType Directory -Path "test-extraction" -Force
Expand-Archive -Path "MenuOps-Production-v1.0.zip" -DestinationPath "test-extraction\production"
Expand-Archive -Path "MenuOps-SelfContained-v1.0.zip" -DestinationPath "test-extraction\self-contained"

# Verify key files exist
Test-Path "test-extraction\production\index.html"
Test-Path "test-extraction\production\AMPSMiddleWare.dll"
Test-Path "test-extraction\self-contained\AMPSMiddleWare.exe"
```

## Step 9: Create Release Notes

### 9.1 Document Package Contents
Create a `RELEASE_NOTES.md` file documenting:
- Version number
- Build date
- Environment configurations included
- Prerequisites for each package type
- Known issues or limitations

## Package Distribution

### Available Packages
1. **MenuOps-Production-v1.0.zip** - Framework-dependent production build
2. **MenuOps-QA-v1.0.zip** - Framework-dependent QA build  
3. **MenuOps-SelfContained-v1.0.zip** - Self-contained build (no .NET runtime required)
4. **MenuOps-Docker-v1.0.zip** - Docker containerization files

### Next Steps
After creating packages, refer to `SERVER_SETUP_GUIDE.md` for instructions on deploying these packages to production servers.

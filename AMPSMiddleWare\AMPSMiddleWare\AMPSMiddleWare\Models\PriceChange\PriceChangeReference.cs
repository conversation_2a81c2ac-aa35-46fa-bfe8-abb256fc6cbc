﻿using AMPSMiddleWare.Models.Owner;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChangeReference : OwnerReference
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? StartDate { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? EffectiveDate { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Guid PriceChangeUniqueID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PriceChangeID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PriceChangeName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int PriceChangePOSNumber { get; set; }
    }
}

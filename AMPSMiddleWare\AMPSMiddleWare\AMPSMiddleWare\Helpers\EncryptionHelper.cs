﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using System.Text;
using System.Security.Cryptography;

namespace AMPSMiddleWare.Helpers
{
    public static class EncryptionHelper
    {
        static string _secretKey = "ThisIsASecretKey";

        static void CheckKeyLength()
        {
            if (_secretKey.Length != 16 )
            {
                if (_secretKey.Length < 16)
                    _secretKey = _secretKey.PadRight(16, 'X');
                else
                    _secretKey = _secretKey.Substring(0, 16);
            }

        }

        public static string EncryptAES(string plainText)
        {
            CheckKeyLength();
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes(_secretKey);
                aesAlg.IV = new byte[16]; // Initialization Vector (IV)

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        public static string DecryptAES(string cipherText)
        {
            CheckKeyLength();
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes(_secretKey);
                aesAlg.IV = new byte[16]; // Initialization Vector (IV)

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msDecrypt = new MemoryStream(Convert.FromBase64String(cipherText)))
                using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }
    }
}

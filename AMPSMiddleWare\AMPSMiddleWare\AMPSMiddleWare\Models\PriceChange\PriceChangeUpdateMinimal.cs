﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace AMPSMiddleWare.Models.PriceChange
{
    public class PriceChangeUpdateMinimal
    {
        public string PriceChangeUniqueID { get; set; }
        public OwnerReferenceMinimalDto Owner { get; set; }
        public DateTime? StartDate { get; set; }
        public List<PriceChangeMenuItemMinimalDto> MenuItemAssignments { get; set; }
        public List<PriceChangePromoAssignmentUnionMinimalDto> PromoAssignments { get; set; }
        [JsonIgnore]
        public DateTime ExecutionDate { get; set; }
    }

    public class PriceChangeMenuItemMinimalDto
    {
        public MenuItemReferenceMinimalDto MenuItem { get; set; }
        public float Price { get; set; }
    }

    public class PriceChangePromoAssignmentUnionMinimalDto
    {
        public PriceChangePromotionQuickComboMinimalDto QuickCombo { get; set; }
    }

    public class PriceChangePromotionQuickComboMinimalDto
    {
        public PromotionReferenceMinimalDto Promotion { get; set; }
        public float? RegularPrice { get; set; }
        public float? Upsell1Price { get; set; }
        public float? Upsell2Price { get; set; }
        public List<PriceChangePromotionQuickComboComponentMinimalDto> Components { get; set; }
    }

    public class OwnerReferenceMinimalDto
    {
        public Guid OwnerUniqueID { get; set; }
    }

    public class PromotionReferenceMinimalDto
    {
        public Guid PromotionUniqueID { get; set; }
    }

    public class PriceChangePromotionQuickComboComponentMinimalDto
    {
        public string ComponentName { get; set; }
        public List<PriceChangePromotionQuickComboComponentItemMinimalDto> Items { get; set; }
    }

    public class PriceChangePromotionQuickComboComponentItemMinimalDto
    {
        public MenuItemReferenceMinimalDto Item { get; set; }
        public float? RegularSurcharge { get; set; }
        public float? Upsell1Surcharge { get; set; }
        public float? Upsell2Surcharge { get; set; }
    }

    public class MenuItemReferenceMinimalDto
    {
        public Guid MenuItemUniqueID { get; set; }
    }
}

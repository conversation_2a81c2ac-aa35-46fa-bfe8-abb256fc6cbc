﻿using AMPSMiddleWare.Enums;
using AMPSMiddleWare.Models.ItemCategory;
using AMPSMiddleWare.Models.Owner;
using AMPSMiddleWare.Models.PriceLevel;
using AMPSMiddleWare.Models.TaxGroup;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace AMPSMiddleWare.Models.MenuItem
{
    public class MenuItem
    {
        public MenuItemType? Type { get; set; }
        public Guid MenuItemUniqueID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string MenuItemID { get; set; }
        public OwnerReference Owner { get; set; }
        public string Name { get; set; }
        public string LongName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string AlternateLongName { get; set; }
        public string ChitName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string AlternateChitName { get; set; }
        public string BOHName { get; set; }
        public ItemCategoryReference Category { get; set; }
        public TaxGroupReference TaxGroup { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public PriceLevelReference PriceLevel { get; set; }
        public int POSNumber { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int ExportId { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MenuItemPricingMethod? PricingMethod { get; set; }
        public float Price { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public float Cost { get; set; }
        public List<MenuItemSKU> SKU { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MenuItemStandard Standard { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MenuItemFamilyStyle FamilyStyle { get; set; }
    }
}

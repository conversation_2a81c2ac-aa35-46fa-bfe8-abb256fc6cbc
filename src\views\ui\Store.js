import React, {useRef, useState } from "react"; 
import * as XLSX from "xlsx";
import { Badge, Button, Card, CardBody, CardTitle,CardText, Row, Col,UncontrolledDropdown,  DropdownToggle,  DropdownMenu,  DropdownItem,  Dropdown,ButtonGroup } from "reactstrap";
import { Link } from 'react-router-dom'
import Globals from '../../Globals';
const fileTypes = ["XLSX"]; 



 

const DatePicker = () => {
  

  return (
    <div>
	  <h1>
			<Badge color="secondary">Select Stores</Badge>
		</h1>
	   
      <p></p>
    
	   <CardBody className="">
               
              <ButtonGroup> 
 			    <Link to = "/update">				
					<Button disabled={Globals.TemplateFileName == ""} color="secondary"><b>Continue</b></Button> 
				</Link> 
			  </ButtonGroup>
				  
		</CardBody> 
	
	</div>
  );
};

export default DatePicker;